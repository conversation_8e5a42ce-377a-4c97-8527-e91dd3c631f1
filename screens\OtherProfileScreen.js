import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  FlatList,
  Alert,
  RefreshControl,
  ToastAndroid,
  Modal,
  Animated,
  Pressable
} from 'react-native';
import {
  doc,
  onSnapshot,
  collection,
  query,
  where,
  updateDoc,
  arrayUnion,
  arrayRemove,
  deleteDoc,
  getDocs,
  getDoc,
  addDoc,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { Ionicons } from '@expo/vector-icons';
import { db, auth } from '../firebase'; // firebase.js'ten import
import { useRoute, useNavigation } from '@react-navigation/native';
import defaultAvatar from '../assets/default-avatar.png';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useFollowStatus } from '../contexts/FollowStatusContext';
import { createNotification, NOTIFICATION_TYPES } from '../utils/notificationUtils';
import { avatarMap } from '../utils/avatarMap';
import { updateUserPopularity, listenToUserPopularity, listenToPostComments } from '../utils/popularityUtils';
import {
  sendFollowRequest,
  cancelFollowRequest,
  getFollowRequest
} from '../utils/followRequestUtils';

/** "x dakika önce" formatı */
const timeAgo = (timestamp) => {
  if (!timestamp || !timestamp.toDate) return "Bilinmeyen Tarih";
  const postDate = timestamp.toDate();
  const now = new Date();
  const diffInSeconds = Math.floor((now - postDate) / 1000);
  if (diffInSeconds < 60) return diffInSeconds + " saniye önce";
  if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + " dakika önce";
  if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + " saat önce";
  return Math.floor(diffInSeconds / 86400) + " gün önce";
};

const OtherProfileScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  // Theme context kullanımı (ileride kullanılabilir)
  useTheme();
  const { translations } = useLanguage();
  const {
    isFollowing: isFollowingGlobal,
    hasPendingRequest,
    handleFollowSuccess,
    handleUnfollowSuccess,
    handleFollowRequestSent,
    handleFollowRequestCancelled
  } = useFollowStatus();
  const { uid } = route.params || {}; // Diğer kullanıcının UID'si
  const currentUid = auth.currentUser?.uid;

  const [userData, setUserData] = useState(null);
  const [posts, setPosts] = useState([]);
  const [loadingProfile, setLoadingProfile] = useState(true);
  const [loadingPosts, setLoadingPosts] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [optionsModalVisible, setOptionsModalVisible] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState(null);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockModalVisible, setBlockModalVisible] = useState(false);
  const [savedPosts, setSavedPosts] = useState([]);
  const [processingLikes, setProcessingLikes] = useState({});
  const likeAnimValues = useRef({});
  const [popularity, setPopularity] = useState(0);
  const [userRank, setUserRank] = useState('');
  const [commentCounts, setCommentCounts] = useState({});

  // Beğeniler modal state'leri
  const [likesModalVisible, setLikesModalVisible] = useState(false);
  const [likesModalData, setLikesModalData] = useState([]);

  // Beğeni işlemi


  // Bu profil benim mi?
  const isMyProfile = currentUid === uid;

  // Takip kontrolü
  const [isFollowing, setIsFollowing] = useState(false);
  const [isPrivateAccount, setIsPrivateAccount] = useState(false);

  // Popülerlik hesaplama fonksiyonu
  const calculatePopularity = (userPosts, followers) => {
    let totalLikes = 0;
    let totalComments = 0;

    // Gönderilerdeki beğeni ve yorumları topla
    userPosts.forEach(post => {
      totalLikes += post.likes || 0;
      totalComments += commentCounts[post.id] || 0;
    });

    // Popülerlik puanı hesaplama: 10 beğeni = 1 puan, 3 yorum = 1 puan, 1 takipçi = 1 puan
    const likesPoints = Math.floor(totalLikes / 10);
    const commentsPoints = Math.floor(totalComments / 3);
    const followersPoints = followers || 0;

    const total = likesPoints + commentsPoints + followersPoints;

    // Popülerlik değerini Firestore'da güncelle
    if (uid) {
      updateDoc(doc(db, 'users', uid), {
        popularity: total
      }).catch(err => console.error('Popülerlik güncelleme hatası:', err));
    }

    return total;
  };

  // Kullanıcı ünvanını belirle
  const determineRank = (popularityScore) => {
    if (popularityScore >= 1000) return 'Efsane';
    if (popularityScore >= 500) return 'Yıldız';
    if (popularityScore >= 200) return 'Popüler';
    if (popularityScore >= 100) return 'Yükselen';
    if (popularityScore >= 50) return 'Aktif';
    if (popularityScore >= 20) return 'Başlangıç';
    return 'Yeni Üye';
  };

  /** Diğer kullanıcının profil verisini çek */
  useEffect(() => {
    if (!uid) {
      setLoadingProfile(false);
      return;
    }
    const userRef = doc(db, 'users', uid);
    const unsubProfile = onSnapshot(
      userRef,
      (snap) => {
        if (snap.exists()) {
          const data = snap.data();
          setUserData(data);
          // Bu yöntem Firestore'daki user dokümanındaki followers dizisini dinler
          setIsFollowing(data.followers?.includes(currentUid));
          // Gizli hesap durumunu kontrol et
          setIsPrivateAccount(data.isPrivateAccount || false);

          // Engelleme durumunu kontrol et
          const currentUserRef = doc(db, 'users', currentUid);
          getDoc(currentUserRef).then(currentUserSnap => {
            if (currentUserSnap.exists()) {
              const currentUserData = currentUserSnap.data();
              const blockedUsers = currentUserData.blockedUsers || [];
              setIsBlocked(blockedUsers.includes(uid));
            }
          });

          // Takip isteği durumunu kontrol et (sadece gizli hesaplar için)
          // Global state zaten bu durumu yönetiyor, ek kontrol gerekmez
        }
        setLoadingProfile(false);
      },
      (err) => {
        console.error("Takip durumu kontrol hatası:", err);
        setLoadingProfile(false);
      }
    );
    return () => unsubProfile();
  }, [uid, currentUid]);

  // Global state zaten takip isteği durumunu yönetiyor, ek polling gerekmez

  // Global state zaten takip durumu değişikliklerini yönetiyor

  // Takip listesi ekranından gelen takip durumu değişikliklerini dinle
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      // Eğer takip durumu değişikliği varsa, profil verilerini yeniden yükle
      if (route.params?.followStatusChanged) {
        // Parametreyi temizle
        navigation.setParams({ followStatusChanged: undefined });

        // Profil verilerini yeniden yükle
        if (uid) {
          const userRef = doc(db, 'users', uid);
          getDoc(userRef).then(snap => {
            if (snap.exists()) {
              const data = snap.data();
              setUserData(data);
              setIsFollowing(data.followers?.includes(currentUid));
            }
          }).catch(err => {
            console.error("Profil yenileme hatası:", err);
          });
        }
      }
    });

    return unsubscribe;
  }, [navigation, route.params?.followStatusChanged, uid, currentUid]);

  /** Ek olarak, "follows" koleksiyonundan da takip durumunu dinle (feed ekranındaki yöntemle uyumlu olması için) */
  useEffect(() => {
    if (!currentUid || !uid) return;
    const q = query(
      collection(db, "follows"),
      where("followerUid", "==", currentUid),
      where("followingUid", "==", uid)
    );
    const unsubscribeFollows = onSnapshot(q, (snapshot) => {
      const isFollowingFromFollows = !snapshot.empty;
      setIsFollowing(isFollowingFromFollows);

      // Debug log
      console.log(`Takip durumu güncellendi (follows): ${currentUid} -> ${uid} = ${isFollowingFromFollows}`);
    });
    return () => unsubscribeFollows();
  }, [currentUid, uid]);

  // Ek kontrol: users koleksiyonundan da takip durumunu dinle
  useEffect(() => {
    if (!currentUid || !uid) return;

    const currentUserRef = doc(db, 'users', currentUid);
    const unsubscribeUser = onSnapshot(currentUserRef, (snapshot) => {
      if (snapshot.exists()) {
        const userData = snapshot.data();
        const followingList = userData.following || [];
        const isFollowingFromUser = followingList.includes(uid);

        // Sadece follows koleksiyonu ile çelişiyorsa güncelle
        setIsFollowing(prev => {
          if (prev !== isFollowingFromUser) {
            console.log(`Takip durumu düzeltildi (users): ${currentUid} -> ${uid} = ${isFollowingFromUser}`);
            return isFollowingFromUser;
          }
          return prev;
        });
      }
    });

    return () => unsubscribeUser();
  }, [currentUid, uid]);

  /** Diğer kullanıcının gönderilerini çek */
  useEffect(() => {
    if (!uid) {
      setLoadingPosts(false);
      return;
    }
    loadPosts();
    loadSavedPosts();
  }, [uid]);

  // Popülerlik ve ünvanı güncelle
  useEffect(() => {
    if (userData && posts.length > 0) {
      const popularityScore = calculatePopularity(posts, userData.followers?.length || 0);
      setPopularity(popularityScore);
      setUserRank(determineRank(popularityScore));
    }
  }, [posts, userData]);

  // Popülerlik değerini canlı olarak dinle
  useEffect(() => {
    if (!uid) return;

    // Popülerlik değişimini dinle
    const unsubscribe = listenToUserPopularity(uid, (newPopularity) => {
      setPopularity(newPopularity);
      setUserRank(determineRank(newPopularity));
    });

    return () => unsubscribe();
  }, [uid]);

  // Yorum sayılarını canlı olarak dinle
  useEffect(() => {
    if (posts.length === 0) return;

    const commentUnsubscribes = {};

    // Her gönderi için yorum sayısını dinle
    posts.forEach(post => {
      commentUnsubscribes[post.id] = listenToPostComments(post.id, (commentCount) => {
        setCommentCounts(prev => ({
          ...prev,
          [post.id]: commentCount
        }));
      });
    });

    // Component unmount olduğunda dinleyicileri temizle
    return () => {
      Object.values(commentUnsubscribes).forEach(unsub => unsub && unsub());
    };
  }, [posts.length]);

  const loadPosts = () => {
    if (!uid) return;
    setLoadingPosts(true);
    const postsRef = collection(db, 'posts');
    const q = query(postsRef, where('uid', '==', uid));

    // Önce kullanıcı bilgilerini al
    getDoc(doc(db, 'users', uid)).then(userDoc => {
      const userData = userDoc.exists() ? userDoc.data() : null;

      // Sonra gönderileri al
      onSnapshot(
        q,
        (snapshot) => {
          const arr = snapshot.docs.map((docSnap) => ({
            id: docSnap.id,
            ...docSnap.data(),
            likedBy: docSnap.data().likedBy || [],
            likes: docSnap.data().likes || 0,
            // Kullanıcı bilgilerini ekle
            username: userData?.username || 'Kullanıcı',
            profilePic: userData?.profilePic || null,
            popularity: userData?.popularity || 0
          }));
          setPosts(arr);

          // Gönderiler yüklendi

          setLoadingPosts(false);
          setRefreshing(false);
        },
        (err) => {
          console.error("Gönderi çekme hatası:", err);
          setLoadingPosts(false);
        }
      );
    }).catch(err => {
      console.error("Kullanıcı bilgisi çekme hatası:", err);
      setLoadingPosts(false);
    });
  };

  /** Kaydedilen gönderileri yükle */
  const loadSavedPosts = async () => {
    if (!currentUid) return;
    try {
      const savedRef = collection(db, 'savedPosts');
      const q = query(savedRef, where('userId', '==', currentUid));
      const snapshot = await getDocs(q);
      const savedIds = snapshot.docs.map(doc => doc.data().postId);
      setSavedPosts(savedIds);
    } catch (error) {
      console.error('Kaydedilen gönderiler yüklenirken hata:', error);
    }
  };

  /** Pull-to-refresh */
  const onRefresh = () => {
    setRefreshing(true);
    loadPosts();
  };

  /** Takip Et / Takipten Çık / Takip İsteği Gönder */
  const toggleFollow = () => {
    if (!currentUid || !uid || !userData) return;

    // Engelleme kontrolü - eğer kullanıcı engellenmiş ise takip işlemi yapılamaz
    if (isBlocked) {
      ToastAndroid.show('Engellediğiniz kullanıcıyı takip edemezsiniz.', ToastAndroid.SHORT);
      return;
    }

    // Gizli hesap kontrolü
    if (isPrivateAccount && !isFollowing) {
      handleFollowRequest();
      return;
    }

    // Normal takip işlemi (açık hesaplar veya zaten takip edilen gizli hesaplar)
    // Önce UI'da güncelleme yap (anında tepki için)
    setIsFollowing(!isFollowing);

    // Takip işlemini arka planda yap
    const updateFollowStatus = async () => {
      try {
        const userDocRef = doc(db, 'users', uid);
        const currentUserDocRef = doc(db, 'users', currentUid);

        if (isFollowing) {
          // Takipten çık - Firestore işlemleri
          updateDoc(userDocRef, {
            followers: arrayRemove(currentUid),
          }).catch(err => console.error('Takipçi kaldırma hatası:', err));

          updateDoc(currentUserDocRef, {
            following: arrayRemove(uid),
          }).catch(err => console.error('Takip kaldırma hatası:', err));

          // follows koleksiyonundan da sil
          const q = query(
            collection(db, "follows"),
            where("followerUid", "==", currentUid),
            where("followingUid", "==", uid)
          );

          getDocs(q).then(snap => {
            snap.forEach(doc => {
              deleteDoc(doc.ref).catch(err => console.error('Follow dokümanı silme hatası:', err));
            });
          }).catch(err => console.error('Follow dokümanı bulma hatası:', err));

          // Popülerlik değerini güncelle
          updateUserPopularity(uid, true).catch(err => console.error('Popülerlik güncelleme hatası:', err));

          // Bildirim göster
          ToastAndroid.show("Takipten çıkıldı", ToastAndroid.SHORT);

          // Global state'i güncelle
          handleUnfollowSuccess(uid);
        } else {
          // Takip et - Firestore işlemleri
          updateDoc(userDocRef, {
            followers: arrayUnion(currentUid),
          }).catch(err => console.error('Takipçi ekleme hatası:', err));

          updateDoc(currentUserDocRef, {
            following: arrayUnion(uid),
          }).catch(err => console.error('Takip ekleme hatası:', err));

          // follows koleksiyonuna ekle
          addDoc(collection(db, 'follows'), {
            followerUid: currentUid,
            followingUid: uid,
            createdAt: serverTimestamp()
          }).catch(err => console.error('Follow dokümanı ekleme hatası:', err));

          // Popülerlik değerini güncelle
          updateUserPopularity(uid, true).catch(err => console.error('Popülerlik güncelleme hatası:', err));

          // Bildirim göster
          ToastAndroid.show("Takip edildi", ToastAndroid.SHORT);

          // Global state'i güncelle
          handleFollowSuccess(uid);
        }
      } catch (error) {
        console.error("Takip toggle hatası:", error);
        // Hata durumunda UI'yı geri al
        setIsFollowing(isFollowing);
        ToastAndroid.show(translations.followError || "Takip işlemi sırasında bir hata oluştu.", ToastAndroid.SHORT);
      }
    };

    // Arka planda işlemi başlat
    updateFollowStatus();
  };

  /** Takip isteği gönder/iptal et */
  const handleFollowRequest = async () => {
    if (!currentUid || !uid) return;

    // Engelleme kontrolü
    if (isBlocked) {
      ToastAndroid.show('Engellediğiniz kullanıcıya takip isteği gönderemezsiniz.', ToastAndroid.SHORT);
      return;
    }

    try {
      if (hasPendingRequest(uid)) {
        // İsteği iptal et
        const success = await cancelFollowRequest(currentUid, uid);
        if (success) {
          handleFollowRequestCancelled(uid);
          ToastAndroid.show("Takip isteği iptal edildi", ToastAndroid.SHORT);
        } else {
          ToastAndroid.show("İstek iptal edilirken hata oluştu", ToastAndroid.SHORT);
        }
      } else {
        // İstek gönder
        const success = await sendFollowRequest(currentUid, uid);
        if (success) {
          handleFollowRequestSent(uid);
          ToastAndroid.show("Takip isteği gönderildi", ToastAndroid.SHORT);
        } else {
          ToastAndroid.show("İstek gönderilirken hata oluştu", ToastAndroid.SHORT);
        }
      }
    } catch (error) {
      console.error('Takip isteği işlemi hatası:', error);
      ToastAndroid.show("Bir hata oluştu", ToastAndroid.SHORT);
    }
  };





  /** Yorum ekranına git */
  const handleCommentPress = (postId) => {
    navigation.navigate('Comments', { postId });
  };

  /** Kullanıcıyı engelleme */
  const blockUser = () => {
    // Engelleme modalını göster
    setBlockModalVisible(true);
  };

  /** Engelleme işlemini gerçekleştir */
  const handleBlock = async () => {
    if (!currentUid || !uid) return;

    try {
      // Önce UI'ı güncelle
      setIsBlocked(true);

      // Modalı kapat
      setBlockModalVisible(false);

      // Kullanıcı engelleme işlemi
      const currentUserRef = doc(db, 'users', currentUid);
      const targetUserRef = doc(db, 'users', uid);

      // ÇİFT YÖNLÜ ENGELLEME - Her iki kullanıcı da birbirini engellesin
      await updateDoc(currentUserRef, {
        blockedUsers: arrayUnion(uid)
      });
      await updateDoc(targetUserRef, {
        blockedUsers: arrayUnion(currentUid)
      });

      // Takip ilişkilerini kaldır
      // 1. Eğer ben onu takip ediyorsam, takipten çık
      if (isFollowing) {
        await updateDoc(currentUserRef, {
          following: arrayRemove(uid)
        });
        await updateDoc(targetUserRef, {
          followers: arrayRemove(currentUid)
        });

        // follows koleksiyonundan da sil
        const followQuery = query(
          collection(db, 'follows'),
          where('followerUid', '==', currentUid),
          where('followingUid', '==', uid)
        );
        const followDocs = await getDocs(followQuery);
        followDocs.forEach(async (doc) => {
          await deleteDoc(doc.ref);
        });

        setIsFollowing(false);
      }

      // 2. Eğer o beni takip ediyorsa, onu takipçilerimden çıkar
      const currentUserDoc = await getDoc(currentUserRef);
      if (currentUserDoc.exists()) {
        const currentUserData = currentUserDoc.data();
        const myFollowers = currentUserData.followers || [];

        if (myFollowers.includes(uid)) {
          await updateDoc(currentUserRef, {
            followers: arrayRemove(uid)
          });
          await updateDoc(targetUserRef, {
            following: arrayRemove(currentUid)
          });

          // follows koleksiyonundan da sil
          const reverseFollowQuery = query(
            collection(db, 'follows'),
            where('followerUid', '==', uid),
            where('followingUid', '==', currentUid)
          );
          const reverseFollowDocs = await getDocs(reverseFollowQuery);
          reverseFollowDocs.forEach(async (doc) => {
            await deleteDoc(doc.ref);
          });
        }
      }

      // Takipten çıkma işlemi
      if (isFollowing) {
        setIsFollowing(false);

        // Takipten çıkma işlemlerini arka planda yap
        const userDocRef = doc(db, 'users', uid);
        const currentUserDocRef = doc(db, 'users', currentUid);

        updateDoc(userDocRef, {
          followers: arrayRemove(currentUid),
        }).catch(err => console.error('Takipçi kaldırma hatası:', err));

        updateDoc(currentUserDocRef, {
          following: arrayRemove(uid),
        }).catch(err => console.error('Takip kaldırma hatası:', err));

        // follows koleksiyonundan da sil
        const q = query(
          collection(db, "follows"),
          where("followerUid", "==", currentUid),
          where("followingUid", "==", uid)
        );

        getDocs(q).then(snap => {
          snap.forEach(doc => {
            deleteDoc(doc.ref).catch(err => console.error('Follow dokümanı silme hatası:', err));
          });
        }).catch(err => console.error('Follow dokümanı bulma hatası:', err));
      }

      // Bildirim göster
      ToastAndroid.show('Kullanıcı engellendi', ToastAndroid.SHORT);

      // Ana sayfaya dön
      navigation.navigate('MainTabs', { screen: 'Ana Sayfa' });
    } catch (error) {
      console.error('Engelleme hatası:', error);
      ToastAndroid.show('Kullanıcı engellenirken bir hata oluştu.', ToastAndroid.SHORT);
    }
  };

  /** Engeli kaldır */
  const handleUnblock = () => {
    if (!currentUid || !uid) return;

    try {
      // Önce UI'ı güncelle
      setIsBlocked(false);

      // Modalı kapat
      setBlockModalVisible(false);

      // Bildirim göster
      ToastAndroid.show('Kullanıcının engeli kaldırıldı', ToastAndroid.SHORT);

      // Kullanıcı engelleme işlemi - arka planda yap
      const currentUserRef = doc(db, 'users', currentUid);
      updateDoc(currentUserRef, {
        blockedUsers: arrayRemove(uid)
      }).catch(error => {
        console.error('Engel kaldırma hatası:', error);
        ToastAndroid.show('Kullanıcının engeli kaldırılırken bir hata oluştu.', ToastAndroid.SHORT);
        // Hata durumunda UI'ı geri al
        setIsBlocked(true);
      });
    } catch (error) {
      console.error('Engel kaldırma hatası:', error);
      ToastAndroid.show('Kullanıcının engeli kaldırılırken bir hata oluştu.', ToastAndroid.SHORT);
      // Hata durumunda UI'ı geri al
      setIsBlocked(true);
    }
  };

  /** Kullanıcıyı bildirme */
  const reportUser = () => {
    // Bildirim seçenekleri için modal göster
    setReportModalVisible(true);
  };

  /** Bildirim gönderme */
  const handleReport = async (reason) => {
    try {
      // Bildirim koleksiyonuna ekle
      await addDoc(collection(db, 'reports'), {
        reportedUserId: uid,
        reportedBy: currentUid,
        reason: reason,
        timestamp: serverTimestamp(),
        status: 'pending'
      });

      // Bildirim göster
      ToastAndroid.show('Bildiriminiz alındı. Teşekkürler!', ToastAndroid.SHORT);
    } catch (error) {
      console.error('Bildirim hatası:', error);
      Alert.alert('Hata', 'Bildirim gönderilirken bir hata oluştu.');
    }
  };

  /** "Üç nokta" butonuna basılınca */
  const handlePostOptions = (postId) => {
    setSelectedPostId(postId);
    setOptionsModalVisible(true);
  };



  /** Gönderi kaydetme */
  const handleSavePost = (postId) => {
    if (!currentUid) return;

    try {
      // Zaten kaydedilmiş mi kontrol et
      const isSaved = savedPosts.includes(postId);

      // Önce UI'ı güncelle (anında tepki için)
      if (isSaved) {
        // Kaydı kaldır - UI güncelleme
        setSavedPosts(prev => prev.filter(id => id !== postId));
        ToastAndroid.show('Gönderi kaydedilenlerden kaldırıldı', ToastAndroid.SHORT);
      } else {
        // Kaydet - UI güncelleme
        setSavedPosts(prev => [...prev, postId]);
        ToastAndroid.show('Gönderi kaydedildi', ToastAndroid.SHORT);
      }

      // Arka planda Firestore işlemlerini yap
      const updateSaveStatus = async () => {
        try {
          if (isSaved) {
            // Kaydı kaldır - Firestore işlemi
            const savedRef = collection(db, 'savedPosts');
            const q = query(savedRef,
              where('userId', '==', currentUid),
              where('postId', '==', postId)
            );
            const snapshot = await getDocs(q);

            if (!snapshot.empty) {
              deleteDoc(snapshot.docs[0].ref).catch(err =>
                console.error('Kayıt silme hatası:', err)
              );
            }
          } else {
            // Kaydet - Firestore işlemi
            addDoc(collection(db, 'savedPosts'), {
              userId: currentUid,
              postId: postId,
              savedAt: serverTimestamp()
            }).catch(err => console.error('Kaydetme hatası:', err));
          }
        } catch (error) {
          console.error('Gönderi kaydetme hatası:', error);

          // Hata durumunda UI'ı geri al
          if (isSaved) {
            setSavedPosts(prev => [...prev, postId]);
          } else {
            setSavedPosts(prev => prev.filter(id => id !== postId));
          }

          ToastAndroid.show('Gönderi kaydedilirken bir hata oluştu', ToastAndroid.SHORT);
        }
      };

      // Arka planda işlemi başlat
      updateSaveStatus();
    } catch (error) {
      console.error('Gönderi kaydetme hatası:', error);
      ToastAndroid.show('Gönderi kaydedilirken bir hata oluştu', ToastAndroid.SHORT);
    }
  };







  // Çift tıklama için
  const lastTapRef = useRef({});
  const doubleTapDelayMs = 300;

  // Çift tıklama ile beğenme fonksiyonu
  const handleDoubleTap = (postId) => {
    // İşlem zaten devam ediyorsa çık
    if (processingLikes[postId]) return;

    // Kullanıcı giriş yapmamışsa çık
    if (!currentUid) return;

    const now = Date.now();
    const lastTap = lastTapRef.current[postId] || 0;

    // Son tıklama zamanını güncelle
    lastTapRef.current[postId] = now;

    // Eğer iki tıklama arasındaki süre belirlenen süreden kısaysa çift tıklama olarak kabul et
    if (now - lastTap < doubleTapDelayMs) {
      // Post'u bul
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      // Beğeni durumunu kontrol et ve beğeni işlemini başlat
      // Çift tıklamada her zaman beğeni işlemini başlat (kalbe basma ile aynı davranış)
      toggleLike(postId);
    }
  };

  /** Beğeni işlemi */
  const toggleLike = (postId) => {
    if (!currentUid) return;

    // Beğeni işlemi devam ediyorsa çık
    if (processingLikes[postId]) return;

    // İşlem başladı - Hemen işlemi kilitle
    setProcessingLikes(prev => ({ ...prev, [postId]: true }));

    // Beğeni animasyonu için Animated.Value oluştur
    if (!likeAnimValues.current[postId]) {
      likeAnimValues.current[postId] = new Animated.Value(1);
    }

    // Önce mevcut durumu kontrol et
    const post = posts.find(p => p.id === postId);
    if (!post) {
      setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      return;
    }

    // Arka planda Firestore işlemlerini yap
    const postRef = doc(db, 'posts', postId);

    getDoc(postRef).then(snap => {
      if (!snap.exists()) {
        // Post bulunamadı
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        return;
      }

      const data = snap.data();
      const serverIsLiked = data.likedBy?.includes(currentUid);

      // Sunucu durumu ile istemci durumu farklıysa, sunucu durumunu kullan
      // Bu, hızlı ardışık beğeni işlemlerinde tutarlılık sağlar
      const finalIsLiked = serverIsLiked;

      // Beğeni sayısı 0'dan küçük olamaz
      const newLikes = finalIsLiked ? Math.max(0, data.likes - 1) : (data.likes || 0) + 1;

      // UI'ı güncelle
      setPosts(prev => {
        return prev.map(p => {
          if (p.id === postId) {
            let updatedLikedBy;

            if (finalIsLiked) {
              // Beğeniyi kaldır
              updatedLikedBy = p.likedBy?.filter(uid => uid !== currentUid) || [];

              // Beğeni kaldırma animasyonu (hafif küçültme)
              Animated.sequence([
                // Önce küçült
                Animated.spring(likeAnimValues.current[postId], {
                  toValue: 0.8,
                  friction: 3,
                  tension: 100,
                  useNativeDriver: true
                }),
                // Sonra normal boyuta getir
                Animated.spring(likeAnimValues.current[postId], {
                  toValue: 1,
                  friction: 3,
                  tension: 80,
                  useNativeDriver: true
                })
              ]).start();
            } else {
              // Beğeni ekle
              updatedLikedBy = [...(p.likedBy || []), currentUid];

              // Beğeni ekleme animasyonu (büyütme)
              Animated.sequence([
                // Önce büyüt
                Animated.spring(likeAnimValues.current[postId], {
                  toValue: 1.3,
                  friction: 2,
                  tension: 120,
                  useNativeDriver: true
                }),
                // Sonra normal boyuta getir
                Animated.spring(likeAnimValues.current[postId], {
                  toValue: 1,
                  friction: 3,
                  tension: 80,
                  useNativeDriver: true
                })
              ]).start();
            }

            return { ...p, likes: newLikes, likedBy: updatedLikedBy };
          }
          return p;
        });
      });

      // Firestore güncelleme
      updateDoc(postRef, {
        likes: newLikes, // increment yerine doğrudan değer atama
        likedBy: finalIsLiked
          ? arrayRemove(currentUid)
          : arrayUnion(currentUid),
        lastLikeUpdate: serverTimestamp()
      }).then(() => {
        // Beğeni bildirimi oluştur
        if (!finalIsLiked && data.uid !== currentUid) {
          createNotification(
            NOTIFICATION_TYPES.LIKE,
            currentUid,
            data.uid,
            postId
          );
        }

        // Popülerlik değerini güncelle
        if (data.uid) {
          // Zorla güncelleme yap (true) ve sonucu bekle
          return updateUserPopularity(data.uid, true);
        }
      }).catch(() => {
        // Beğeni güncelleme hatası
      }).finally(() => {
        // İşlem bitti - 500ms sonra kilidi kaldır (hızlı ardışık tıklamaları engelle)
        setTimeout(() => {
          setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        }, 500);
      });
    }).catch(() => {
      // toggleLike hata
      // İşlem bitti - 500ms sonra kilidi kaldır
      setTimeout(() => {
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      }, 500);
    });
  };

  // Beğenenleri göster
  const handleShowLikes = (likedBy) => {
    if (!likedBy || likedBy.length === 0) return;

    setLikesModalData(likedBy);
    setLikesModalVisible(true);
  };

  /** Gönderi item */
  const renderPostItem = ({ item }) => {
    const isLiked = (item.likedBy || []).includes(currentUid);
    const isSaved = savedPosts.includes(item.id);

    // Beğeni animasyonu için
    if (!likeAnimValues.current[item.id]) {
      likeAnimValues.current[item.id] = new Animated.Value(1);
    }

    // Kullanıcı ünvanını belirle
    const userRank =
      item.popularity >= 1000 ? 'Efsane' :
      item.popularity >= 500 ? 'Yıldız' :
      item.popularity >= 200 ? 'Popüler' :
      item.popularity >= 100 ? 'Yükselen' :
      item.popularity >= 50 ? 'Aktif' :
      item.popularity >= 20 ? 'Başlangıç' :
      item.popularity > 0 ? 'Yeni Üye' : '';

    return (
      <View style={styles.card}>
        <View style={styles.postHeader}>
          <View style={styles.headerLeft}>
            <Image
              source={
                item.profilePic && avatarMap[item.profilePic]
                  ? avatarMap[item.profilePic]
                  : require('../assets/default-avatar.png')
              }
              style={styles.avatar}
            />
            <View style={styles.headerInfo}>
              <Text style={styles.username}>{item.username || 'Kullanıcı'}</Text>
              <Text style={styles.userRank}>{userRank || 'Yeni Üye'}</Text>
            </View>
          </View>

          <View style={styles.headerRight}>
            <Text style={styles.postTypeLabel}>
              {item.type === 'Şarkı Sözü' ? 'Şarkı' : (item.type === 'Şiir' ? 'Şiir' : item.type)}
            </Text>
            <TouchableOpacity style={styles.optionsButton} onPress={() => handlePostOptions(item.id)}>
              <Ionicons name="ellipsis-horizontal" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>

        <TouchableOpacity
          style={styles.cardContent}
          activeOpacity={0.9}
          onPress={() => handleDoubleTap(item.id)}
        >
          <Text style={styles.content}>{item.content}</Text>
        </TouchableOpacity>

        <View style={styles.cardFooter}>
          <View style={styles.footerLeft}>
            <TouchableOpacity
              style={styles.likeContainer}
              onPress={() => toggleLike(item.id)}
              onLongPress={() => handleShowLikes(item.likedBy)}
              disabled={processingLikes[item.id]}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Animated.View style={{ transform: [{ scale: likeAnimValues.current[item.id] }] }}>
                <Ionicons
                  name={isLiked ? "heart" : "heart-outline"}
                  size={24}
                  color={isLiked ? "#e74c3c" : "#fff"}
                />
              </Animated.View>
              <Text style={[styles.actionText, { marginLeft: 6 }]}>{item.likes}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { marginLeft: -2 }]}
              onPress={() => handleCommentPress(item.id)}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Ionicons name="chatbubble-outline" size={24} color="#fff" />
              <Text style={styles.actionText}>Yorum ({item.commentsCount || 0})</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { marginLeft: 10 }]}
              onPress={() => handleSavePost(item.id)}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Ionicons
                name={isSaved ? "bookmark" : "bookmark-outline"}
                size={22}
                color="#fff"
              />
              <Text style={styles.actionText}>{isSaved ? "Kaydedildi" : "Kaydet"}</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.timestampFooter}>
            {timeAgo(item.createdAt)}
          </Text>
        </View>
      </View>
    );
  };

  /** Profil Üst Kısmı */
  const renderProfileHeader = () => {
    if (loadingProfile) {
      return (
        <View style={{ padding: 20 }}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      );
    }
    if (!userData) {
      return (
        <View style={{ padding: 20 }}>
          <Text style={{ color: '#fff' }}>{translations.userNotFound || "Kullanıcı bulunamadı."}</Text>
        </View>
      );
    }
    const followersCount = userData.followers?.length || 0;
    const followingCount = userData.following?.length || 0;
    const postsCount = posts.length;
    const buttonLabel = isMyProfile
      ? (translations.editProfile || "Profili Düzenle")
      : isFollowing
        ? (translations.unfollow || "Takipten Çık")
        : isPrivateAccount
          ? (hasPendingRequest(uid) ? "İstek Gönderildi" : "İstek Gönder")
          : (translations.follow || "Takip Et");

    return (
      <View style={styles.profileSection}>
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <Image
              source={
                userData.profilePic && avatarMap[userData.profilePic]
                  ? avatarMap[userData.profilePic]
                  : defaultAvatar
              }
              style={styles.profileImage}
            />
            <View style={styles.popularityContainer}>
              <Text style={styles.userRank}>{popularity} popülerlik</Text>
            </View>
            {userRank && (
              <View style={styles.rankContainer}>
                <Text style={styles.rankText}>{userRank}</Text>
              </View>
            )}
          </View>
          <View style={styles.infoContainer}>
            <View style={styles.nameContainer}>
              <Text style={styles.name}>{userData.username || (translations.user || 'Kullanıcı')}</Text>
            </View>
            <Text
              style={[
                styles.bio,
                userData?.bio && userData.bio.length > 100 ? { fontSize: 10 } :
                userData?.bio && userData.bio.length > 80 ? { fontSize: 11 } :
                userData?.bio && userData.bio.length > 60 ? { fontSize: 12 } :
                userData?.bio && userData.bio.length > 40 ? { fontSize: 13 } :
                { fontSize: 14 }
              ]}
              numberOfLines={5}
            >
              {userData?.bio || 'Henüz biyografi eklenmemiş.'}
            </Text>
            <View style={styles.statsContainer}>
              <View style={styles.stat}>
                <Text style={styles.statNumber}>{postsCount}</Text>
                <Text style={styles.statLabel}>Gönderi</Text>
              </View>
              <TouchableOpacity
                style={styles.stat}
                onPress={() => {
                  if (isPrivateAccount && !isFollowing && !isMyProfile) {
                    ToastAndroid.show("Bu hesap gizli. Takipçi listesini görmek için takip etmelisin.", ToastAndroid.LONG);
                    return;
                  }
                  navigation.navigate('FollowListScreen', {
                    type: 'followers',
                    userIds: userData.followers || [],
                    isCurrentUserProfile: false,
                    profileUid: uid
                  });
                }}
              >
                <Text style={styles.statNumber}>{followersCount}</Text>
                <Text style={styles.statLabel}>Takipçi</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.stat}
                onPress={() => {
                  if (isPrivateAccount && !isFollowing && !isMyProfile) {
                    ToastAndroid.show("Bu hesap gizli. Takip edilen listesini görmek için takip etmelisin.", ToastAndroid.LONG);
                    return;
                  }
                  navigation.navigate('FollowListScreen', {
                    type: 'following',
                    userIds: userData.following || [],
                    isCurrentUserProfile: false,
                    profileUid: uid
                  });
                }}
              >
                <Text style={styles.statNumber}>{followingCount}</Text>
                <Text style={styles.statLabel}>Takip</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={styles.editButton}
          onPress={() => {
            if (isMyProfile) {
              navigation.navigate('ProfiliDüzenle');
            } else {
              toggleFollow();
            }
          }}
        >
          <Text style={styles.editButtonText}>{buttonLabel}</Text>
        </TouchableOpacity>

        <Text style={styles.sectionTitle}>{translations.posts || "Gönderiler"}</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>

      {/* Üst Çubuk: marginTop 15 ile biraz aşağıya alındı */}
      <View style={styles.topHeader}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.topHeaderText}>VELMORA</Text>

        {!isMyProfile && (
          <View style={styles.topHeaderRight}>
            <TouchableOpacity style={styles.topHeaderButton} onPress={reportUser}>
              <Ionicons name="flag-outline" size={24} color="#fff" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.topHeaderButton} onPress={blockUser}>
              <Ionicons name="ban-outline" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        )}
      </View>

      {loadingPosts && posts.length === 0 ? (
        <ActivityIndicator size="large" color="#fff" style={{ marginTop: 50 }} />
      ) : (
        <FlatList
          data={isPrivateAccount && !isFollowing && !isMyProfile ? [] : posts}
          keyExtractor={(item) => item.id}
          renderItem={renderPostItem}
          ListHeaderComponent={renderProfileHeader}
          ListEmptyComponent={
            !loadingPosts && (
              isPrivateAccount && !isFollowing && !isMyProfile ? (
                <View style={styles.privateAccountContainer}>
                  <Ionicons name="lock-closed" size={64} color="#666" />
                  <Text style={styles.privateAccountText}>Bu hesap gizli</Text>
                  <Text style={styles.privateAccountSubtext}>
                    Gönderilerini görmek için takip etmelisin
                  </Text>
                </View>
              ) : (
                <Text style={styles.noPostsText}>Henüz gönderi eklenmemiş.</Text>
              )
            )
          }
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />
          }
          contentContainerStyle={{ paddingBottom: 30 }}
        />
      )}

      {/* Bildirim Modal */}
      <Modal
        visible={reportModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setReportModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Kullanıcıyı Bildir</Text>
            <Text style={styles.modalSubtitle}>Bu kullanıcıyı hangi sebeple bildirmek istiyorsunuz?</Text>

            <TouchableOpacity
              style={styles.modalOption}
              onPress={() => {
                handleReport('spam');
                setReportModalVisible(false);
              }}
            >
              <Ionicons name="mail-unread-outline" size={24} color="#FF6B6B" />
              <Text style={styles.modalOptionText}>Spam</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modalOption}
              onPress={() => {
                handleReport('inappropriate');
                setReportModalVisible(false);
              }}
            >
              <Ionicons name="alert-circle-outline" size={24} color="#FF6B6B" />
              <Text style={styles.modalOptionText}>Uygunsuz İçerik</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modalOption}
              onPress={() => {
                handleReport('harassment');
                setReportModalVisible(false);
              }}
            >
              <Ionicons name="warning-outline" size={24} color="#FF6B6B" />
              <Text style={styles.modalOptionText}>Taciz</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modalOption}
              onPress={() => {
                handleReport('fake_account');
                setReportModalVisible(false);
              }}
            >
              <Ionicons name="person-outline" size={24} color="#FF6B6B" />
              <Text style={styles.modalOptionText}>Sahte Hesap</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modalCancelButton}
              onPress={() => setReportModalVisible(false)}
            >
              <Text style={styles.modalCancelText}>İptal</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Gönderi Seçenekleri Modal */}
      <Modal
        visible={optionsModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setOptionsModalVisible(false)}
      >
        <Pressable style={styles.modalOverlay} onPress={() => setOptionsModalVisible(false)}>
          <View style={styles.optionsModalContainer}>
            <Text style={styles.optionsModalTitle}>Seçenekler</Text>
            {selectedPostId && posts.find(p => p.id === selectedPostId)?.uid === currentUid ? (
              <TouchableOpacity
                style={styles.optionsModalOption}
                onPress={() => {
                  // Gönderiyi sil
                  const post = posts.find(p => p.id === selectedPostId);
                  if (post) {
                    // Silme işlemi
                    ToastAndroid.show('Gönderi silindi', ToastAndroid.SHORT);
                  }
                  setOptionsModalVisible(false);
                }}
              >
                <Ionicons name="trash-outline" size={22} color="#FF4444" />
                <Text style={[styles.optionsModalOptionText, { color: '#FF4444' }]}>Gönderiyi Sil</Text>
              </TouchableOpacity>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.optionsModalOption}
                  onPress={() => {
                    // Gönderiyi bildir
                    const post = posts.find(p => p.id === selectedPostId);
                    if (post) {
                      addDoc(collection(db, 'reports'), {
                        postId: selectedPostId,
                        reportedBy: currentUid,
                        reason: 'inappropriate',
                        timestamp: serverTimestamp(),
                        status: 'pending'
                      }).then(() => {
                        ToastAndroid.show('Gönderi bildirildi', ToastAndroid.SHORT);
                      }).catch(err => {
                        console.error('Gönderi bildirim hatası:', err);
                        ToastAndroid.show('Gönderi bildirilirken bir hata oluştu', ToastAndroid.SHORT);
                      });
                    }
                    setOptionsModalVisible(false);
                  }}
                >
                  <Ionicons name="alert-circle-outline" size={22} color="#FF8888" />
                  <Text style={styles.optionsModalOptionText}>Bildir</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.optionsModalOption}
                  onPress={() => {
                    // Kullanıcıyı engelle
                    blockUser();
                    setOptionsModalVisible(false);
                  }}
                >
                  <Ionicons name="hand-left-outline" size={22} color="#FF8888" />
                  <Text style={styles.optionsModalOptionText}>Engelle</Text>
                </TouchableOpacity>
              </>
            )}
            <TouchableOpacity
              style={[styles.optionsModalOption, { justifyContent: 'center' }]}
              onPress={() => setOptionsModalVisible(false)}
            >
              <Text style={[styles.optionsModalOptionText, { color: '#aaa' }]}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </Pressable>
      </Modal>

      {/* Engelleme Modal */}
      <Modal
        visible={blockModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setBlockModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>{isBlocked ? 'Engeli Kaldır' : 'Kullanıcıyı Engelle'}</Text>
            <Text style={styles.modalSubtitle}>
              {isBlocked
                ? 'Bu kullanıcının engelini kaldırmak istediğinize emin misiniz?'
                : 'Bu kullanıcıyı engellemek istediğinize emin misiniz?'}
            </Text>

            {isBlocked ? (
              <TouchableOpacity
                style={styles.modalOption}
                onPress={handleUnblock}
              >
                <Ionicons name="checkmark-circle-outline" size={24} color="#4A90E2" />
                <Text style={styles.modalOptionText}>Engeli Kaldır</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={styles.modalOption}
                onPress={handleBlock}
              >
                <Ionicons name="ban-outline" size={24} color="#FF6B6B" />
                <Text style={styles.modalOptionText}>Engelle</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.modalCancelButton}
              onPress={() => setBlockModalVisible(false)}
            >
              <Text style={styles.modalCancelText}>İptal</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Beğeniler Modal */}
      {likesModalVisible && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={likesModalVisible}
          onRequestClose={() => setLikesModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Beğenenler</Text>
                <TouchableOpacity
                  onPress={() => setLikesModalVisible(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              <FlatList
                data={likesModalData}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item: userId }) => (
                  <LikeUserItem userId={userId} />
                )}
                style={styles.modalList}
              />
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
};

// Beğeni modal'ındaki kullanıcı item'ı
const LikeUserItem = ({ userId }) => {
  const [userInfo, setUserInfo] = useState(null);
  const navigation = useNavigation();
  const currentUser = auth.currentUser;

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          setUserInfo(userSnap.data());
        }
      } catch (error) {
        console.error('Kullanıcı bilgisi alınırken hata:', error);
      }
    };

    fetchUserInfo();
  }, [userId]);

  if (!userInfo) return null;

  return (
    <TouchableOpacity
      style={styles.likeUserItem}
      onPress={() => {
        if (userId === currentUser?.uid) {
          navigation.navigate('MainTabs', { screen: 'Profil' });
        } else {
          navigation.navigate('OtherProfile', { uid: userId });
        }
      }}
    >
      <Image
        source={
          userInfo.profilePic && avatarMap[userInfo.profilePic]
            ? avatarMap[userInfo.profilePic]
            : require('../assets/default-avatar.png')
        }
        style={styles.likeUserAvatar}
      />
      <Text style={styles.likeUserName}>
        {userInfo.username || 'Kullanıcı'}
      </Text>
    </TouchableOpacity>
  );
};

export default OtherProfileScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  // Üst Çubuk: marginTop 15 ile aşağıya alındı
  topHeader: {
    width: '100%',
    height: 60,
    paddingVertical: 10,
    marginTop: 15,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: '#000000',
  },
  topHeaderRight: {
    position: 'absolute',
    right: 15,
    flexDirection: 'row',
  },
  topHeaderButton: {
    padding: 5,
    marginLeft: 10,
  },
  backButton: {
    position: 'absolute',
    left: 15,
    padding: 5,
  },
  topHeaderText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },

  // FeedScreen'den alınan stiller
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  saveButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  saveText: {
    fontSize: 14,
    color: '#fff',
    marginLeft: 5,
  },
  actionText: {
    fontSize: 14,
    color: '#fff',
    marginLeft: 5,
  },

  // Modal Stilleri
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#1a1a1a',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: 30,
  },
  modalTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalSubtitle: {
    color: '#aaa',
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  modalOptionText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 15,
  },
  modalCancelButton: {
    marginTop: 20,
    paddingVertical: 15,
    alignItems: 'center',
    backgroundColor: '#333',
    borderRadius: 10,
  },
  modalCancelText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },

  // Options Modal Styles (from FeedScreen)
  optionsModalContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#1a1a1a',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  optionsModalTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  optionsModalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  optionsModalOptionText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 15,
  },

  // Profil alanı
  profileSection: {
    backgroundColor: '#000',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    height: 180, // Sabit yükseklik
  },
  avatarContainer: {
    alignItems: 'center',
    marginRight: 10,
    width: 110, // Sabit genişlik
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#0066CC',
  },
  popularityContainer: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderRadius: 12,
    paddingVertical: 3,
    paddingHorizontal: 8,
    marginTop: 13,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#FFD700',
    width: '100%',
  },
  rankContainer: {
    backgroundColor: '#FFD700',
    borderRadius: 12,
    paddingVertical: 2,
    paddingHorizontal: 8,
    marginTop: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '80%',
    height: 22,
  },
  rankText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
    lineHeight: 18,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  infoContainer: {
    flex: 1,
    marginLeft: 5,
    minHeight: 180, // Minimum yükseklik
    position: 'relative', // Göreceli konumlandırma
    paddingBottom: 60, // statsContainer için alan
    justifyContent: 'flex-start', // İçeriği üstten başlat
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    flexWrap: 'wrap',
  },
  name: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginRight: 8,
    flexShrink: 1,
  },
  rankBadge: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
    backgroundColor: '#FFD700',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    overflow: 'hidden',
    marginLeft: 2,
    marginTop: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  bio: {
    fontSize: 14, // Başlangıç yazı boyutu
    color: '#aaa',
    marginVertical: 5,
    flexShrink: 1, // İçeriğe göre küçülmesine izin ver
    flexWrap: 'wrap', // Metni sarma
    lineHeight: 16, // Satır yüksekliğini azalt
  },
  popularityText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#3498db',
    textShadowColor: 'rgba(52, 152, 219, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    letterSpacing: 0.5,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    marginVertical: 10,
    justifyContent: 'space-around',
    height: 50, // Sabit yükseklik
    position: 'absolute', // Mutlak konumlandırma
    bottom: 0, // Alt kısma sabitle
    left: 0,
    right: 0,
  },
  stat: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    height: 50, // Sabit yükseklik
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    height: 25, // Sabit yükseklik
    textAlignVertical: 'center',
  },
  statLabel: {
    fontSize: 14,
    color: '#aaa',
    lineHeight: 18,
    height: 20, // Sabit yükseklik
    textAlignVertical: 'center',
  },
  editButton: {
    backgroundColor: '#0066CC',
    paddingVertical: 12,
    borderRadius: 20,
    marginTop: 10,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  editButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
    lineHeight: 22,
  },
  noPostsText: {
    color: '#fff',
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
  },

  // Gönderi Kartları
  card: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 15,
    marginHorizontal: 10,
    marginBottom: 15,
  },
  // Post Header
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#0066CC',
  },
  headerInfo: {
    marginLeft: 10,
    marginTop: -5,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3498db',
    marginBottom: 2,
  },
  userRank: {
    fontSize: 12,
    color: '#FFD700',
    fontStyle: 'italic',
    marginTop: 1,
    textShadowColor: 'rgba(255, 215, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  timestamp: {
    fontSize: 12,
    color: '#aaa',
    marginTop: 2,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  postTypeLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    marginRight: 8,
  },
  optionsButton: {
    padding: 5,
  },
  // Post İçerik
  cardContent: {
    marginVertical: 10,
  },
  content: {
    fontSize: 15,
    color: '#fff',
    lineHeight: 22,
  },
  // Post Footer
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#333',
    paddingTop: 10,
    justifyContent: 'space-between',
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 5,
  },
  timestampFooter: {
    fontSize: 12,
    color: '#aaa',
  },
  privateAccountContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  privateAccountText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
  privateAccountSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalList: {
    maxHeight: 300,
  },
  likeUserItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 5,
  },
  likeUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  likeUserName: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
});
