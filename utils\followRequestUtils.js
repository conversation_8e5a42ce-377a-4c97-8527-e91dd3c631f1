import { 
  collection, 
  addDoc, 
  deleteDoc, 
  doc, 
  query, 
  where, 
  getDocs, 
  getDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../firebase';
import { createNotification, deleteNotification, NOTIFICATION_TYPES } from './notificationUtils';

/**
 * Takip isteği gönder
 * @param {string} fromUserId - İstek gönderen kullanıcının ID'si
 * @param {string} toUserId - İstek gönderilecek kullanıcının ID'si
 * @returns {Promise<boolean>} - İşlem başarılı mı
 */
export const sendFollowRequest = async (fromUserId, toUserId) => {
  try {
    // Zaten istek gönderilmiş mi kontrol et
    const existingRequest = await getFollowRequest(fromUserId, toUserId);
    if (existingRequest) {
      console.log('Takip isteği zaten gönderilmiş');
      return false;
    }

    // Engelleme kontrolü - gönderen kullanıcının engellenen listesini kontrol et
    const fromUserDoc = await getDoc(doc(db, 'users', fromUserId));
    if (fromUserDoc.exists()) {
      const fromUserData = fromUserDoc.data();
      const fromUserBlockedUsers = fromUserData.blockedUsers || [];
      if (fromUserBlockedUsers.includes(toUserId)) {
        console.log('Engellediğiniz kullanıcıya takip isteği gönderemezsiniz');
        return false;
      }
    }

    // Hedef kullanıcının gönderen kullanıcıyı engellemiş olup olmadığını kontrol et
    const toUserDoc = await getDoc(doc(db, 'users', toUserId));
    if (toUserDoc.exists()) {
      const toUserData = toUserDoc.data();
      const toUserBlockedUsers = toUserData.blockedUsers || [];
      if (toUserBlockedUsers.includes(fromUserId)) {
        console.log('Bu kullanıcı sizi engellediği için takip isteği gönderemezsiniz');
        return false;
      }
    }

    // Takip isteği oluştur
    const followRequestData = {
      fromUserId,
      toUserId,
      status: 'pending',
      createdAt: serverTimestamp()
    };

    await addDoc(collection(db, 'followRequests'), followRequestData);

    // Bildirim oluştur
    await createNotification(
      NOTIFICATION_TYPES.FOLLOW_REQUEST,
      fromUserId,
      toUserId
    );

    return true;
  } catch (error) {
    console.error('Takip isteği gönderme hatası:', error);
    return false;
  }
};

/**
 * Takip isteğini kabul et
 * @param {string} fromUserId - İstek gönderen kullanıcının ID'si
 * @param {string} toUserId - İstek alan kullanıcının ID'si
 * @returns {Promise<boolean>} - İşlem başarılı mı
 */
export const acceptFollowRequest = async (fromUserId, toUserId) => {
  try {
    // Takip isteğini bul ve sil
    const requestDoc = await getFollowRequest(fromUserId, toUserId);
    if (!requestDoc) {
      console.log('Takip isteği bulunamadı');
      return false;
    }

    // İsteği sil
    await deleteDoc(doc(db, 'followRequests', requestDoc.id));

    // Takip ilişkisini oluştur
    await updateDoc(doc(db, 'users', toUserId), {
      followers: arrayUnion(fromUserId)
    });

    await updateDoc(doc(db, 'users', fromUserId), {
      following: arrayUnion(toUserId)
    });

    // follows koleksiyonuna ekle
    await addDoc(collection(db, 'follows'), {
      followerUid: fromUserId,
      followingUid: toUserId,
      createdAt: serverTimestamp()
    });

    // Takip isteği bildirimini sil
    await deleteNotification(
      NOTIFICATION_TYPES.FOLLOW_REQUEST,
      fromUserId,
      toUserId
    );

    // Kabul edildi bildirimi gönder
    await createNotification(
      NOTIFICATION_TYPES.FOLLOW_REQUEST_ACCEPTED,
      toUserId,
      fromUserId
    );

    return true;
  } catch (error) {
    console.error('Takip isteği kabul etme hatası:', error);
    return false;
  }
};

/**
 * Takip isteğini reddet
 * @param {string} fromUserId - İstek gönderen kullanıcının ID'si
 * @param {string} toUserId - İstek alan kullanıcının ID'si
 * @returns {Promise<boolean>} - İşlem başarılı mı
 */
export const rejectFollowRequest = async (fromUserId, toUserId) => {
  try {
    // Takip isteğini bul ve sil
    const requestDoc = await getFollowRequest(fromUserId, toUserId);
    if (!requestDoc) {
      console.log('Takip isteği bulunamadı');
      return false;
    }

    // İsteği sil
    await deleteDoc(doc(db, 'followRequests', requestDoc.id));

    // Takip isteği bildirimini sil
    await deleteNotification(
      NOTIFICATION_TYPES.FOLLOW_REQUEST,
      fromUserId,
      toUserId
    );

    return true;
  } catch (error) {
    console.error('Takip isteği reddetme hatası:', error);
    return false;
  }
};

/**
 * Takip isteğini iptal et (gönderen tarafından)
 * @param {string} fromUserId - İstek gönderen kullanıcının ID'si
 * @param {string} toUserId - İstek gönderilecek kullanıcının ID'si
 * @returns {Promise<boolean>} - İşlem başarılı mı
 */
export const cancelFollowRequest = async (fromUserId, toUserId) => {
  try {
    // Takip isteğini bul ve sil
    const requestDoc = await getFollowRequest(fromUserId, toUserId);
    if (!requestDoc) {
      console.log('Takip isteği bulunamadı');
      return false;
    }

    // İsteği sil
    await deleteDoc(doc(db, 'followRequests', requestDoc.id));

    // Takip isteği bildirimini sil
    await deleteNotification(
      NOTIFICATION_TYPES.FOLLOW_REQUEST,
      fromUserId,
      toUserId
    );

    return true;
  } catch (error) {
    console.error('Takip isteği iptal etme hatası:', error);
    return false;
  }
};

/**
 * Takip isteği var mı kontrol et
 * @param {string} fromUserId - İstek gönderen kullanıcının ID'si
 * @param {string} toUserId - İstek gönderilecek kullanıcının ID'si
 * @returns {Promise<Object|null>} - Takip isteği varsa döküman, yoksa null
 */
export const getFollowRequest = async (fromUserId, toUserId) => {
  try {
    const q = query(
      collection(db, 'followRequests'),
      where('fromUserId', '==', fromUserId),
      where('toUserId', '==', toUserId),
      where('status', '==', 'pending')
    );

    const snapshot = await getDocs(q);
    if (!snapshot.empty) {
      const doc = snapshot.docs[0];
      return { id: doc.id, ...doc.data() };
    }

    return null;
  } catch (error) {
    console.error('Takip isteği kontrol hatası:', error);
    return null;
  }
};

/**
 * Kullanıcının gelen takip isteklerini getir
 * @param {string} userId - Kullanıcının ID'si
 * @returns {Promise<Array>} - Takip istekleri listesi
 */
export const getIncomingFollowRequests = async (userId) => {
  try {
    const q = query(
      collection(db, 'followRequests'),
      where('toUserId', '==', userId),
      where('status', '==', 'pending')
    );

    const snapshot = await getDocs(q);
    const requests = [];

    for (const docSnap of snapshot.docs) {
      const requestData = { id: docSnap.id, ...docSnap.data() };
      
      // Gönderen kullanıcının bilgilerini getir
      const userDoc = await getDoc(doc(db, 'users', requestData.fromUserId));
      if (userDoc.exists()) {
        requestData.fromUser = userDoc.data();
      }

      requests.push(requestData);
    }

    return requests;
  } catch (error) {
    console.error('Gelen takip istekleri getirme hatası:', error);
    return [];
  }
};

/**
 * Kullanıcının gönderdiği takip isteklerini getir
 * @param {string} userId - Kullanıcının ID'si
 * @returns {Promise<Array>} - Takip istekleri listesi
 */
export const getOutgoingFollowRequests = async (userId) => {
  try {
    const q = query(
      collection(db, 'followRequests'),
      where('fromUserId', '==', userId),
      where('status', '==', 'pending')
    );

    const snapshot = await getDocs(q);
    const requests = [];

    for (const docSnap of snapshot.docs) {
      const requestData = { id: docSnap.id, ...docSnap.data() };
      
      // Alıcı kullanıcının bilgilerini getir
      const userDoc = await getDoc(doc(db, 'users', requestData.toUserId));
      if (userDoc.exists()) {
        requestData.toUser = userDoc.data();
      }

      requests.push(requestData);
    }

    return requests;
  } catch (error) {
    console.error('Gönderilen takip istekleri getirme hatası:', error);
    return [];
  }
};
