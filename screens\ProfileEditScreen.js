// screens/ProfileEditScreen.js
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  FlatList,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { auth, db } from '../firebase';
import {
  doc,
  getDoc,
  updateDoc,
  collection,
  query,
  where,
  getDocs,
} from 'firebase/firestore';
import { useLanguage } from '../contexts/LanguageContext';

/** CONFIGURATION */
const BIO_MAX_LENGTH = 50;
const USERNAME_MAX_LENGTH = 30;
const USERNAME_LOCK_PERIOD_MS = 15 * 24 * 60 * 60 * 1000; // 15 days
const MAX_USERNAME_CHANGES_WITHIN_LOCK = 2;

// 14 avatars - please make sure you have 'avatar1.png' ... 'avatar14.png' files in your assets folder
const DUMMY_AVATARS = [
  require('../assets/avatar1.png'),
  require('../assets/avatar2.png'),
  require('../assets/avatar3.png'),
  require('../assets/avatar4.png'),
  require('../assets/avatar5.png'),
  require('../assets/avatar6.png'),
  require('../assets/avatar7.png'),
  require('../assets/avatar8.png'),
  require('../assets/avatar9.png'),
  require('../assets/avatar10.png'),
  require('../assets/avatar11.png'),
  require('../assets/avatar12.png'),
  require('../assets/avatar13.png'),
  require('../assets/avatar14.png'),
];

// Custom notification modal
function MessageModal({ visible, message, onClose }) {
  return (
    <View style={[styles.msgOverlay, { display: visible ? 'flex' : 'none' }]}>
      <View style={styles.msgContainer}>
        <Text style={styles.msgText}>{message}</Text>
        <TouchableOpacity style={styles.msgButton} onPress={onClose}>
          <Text style={styles.msgButtonText}>Tamam</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

function ProfileEditScreen() {
  const navigation = useNavigation();
  const { translations } = useLanguage();

  const [loadingProfile, setLoadingProfile] = useState(true);
  const [saving, setSaving] = useState(false);

  // Profile fields
  const [avatarIndex, setAvatarIndex] = useState(-1); // -1 = avatar seçilmemiş
  const [username, setUsername] = useState('');
  const [originalUsername, setOriginalUsername] = useState('');
  const [bio, setBio] = useState('');
  const [gender, setGender] = useState(''); // "Male", "Female", "Other"

  // Username change information
  const [lastUsernameChange, setLastUsernameChange] = useState(0);
  const [usernameChangeCount, setUsernameChangeCount] = useState(0);

  // Notification modal
  const [messageModalVisible, setMessageModalVisible] = useState(false);
  const [messageModalText, setMessageModalText] = useState('');

  // Stylish modal notification for feedback
  const showMessage = (msg) => {
    setMessageModalText(msg);
    setMessageModalVisible(true);
  };

  // Header with back button
  const renderHeader = () => (
    <View style={styles.headerBar}>
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          navigation.navigate('MainTabs', { screen: 'Profil' });
        }}
        style={styles.backButtonHeader}
      >
        <Ionicons name="arrow-back" size={24} color="#fff" />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Profili Düzenle</Text>
    </View>
  );

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userRef = doc(db, 'users', auth.currentUser.uid);
        const snap = await getDoc(userRef);
        if (!snap.exists()) {
          showMessage(translations.userNotFound || 'User document not found.');
          return;
        }
        const data = snap.data();
        const fetchedUsername = data.username || '';
        setUsername(fetchedUsername);
        setOriginalUsername(fetchedUsername);
        setBio(data.bio || '');
        setGender(data.gender || '');
        if (typeof data.avatarIndex === 'number') {
          setAvatarIndex(data.avatarIndex);
        } else {
          // Eğer avatarIndex yoksa -1 olarak ayarla (avatar seçilmemiş)
          setAvatarIndex(-1);
        }
        setLastUsernameChange(data.lastUsernameChange || 0);
        setUsernameChangeCount(data.usernameChangeCount || 0);
      } catch (err) {
        showMessage(translations.somethingWentWrong || 'An error occurred while fetching profile data.');
      } finally {
        setLoadingProfile(false);
      }
    };
    fetchUserData();
  }, []);

  async function handleSaveProfile() {
    const trimmedUsername = username.trim().toLowerCase();
    const trimmedBio = bio.trim();
    const trimmedGender = gender.trim();

    if (!trimmedUsername || !trimmedGender) {
      showMessage('Kullanıcı adı ve cinsiyet boş bırakılamaz.');
      return;
    }
    if (trimmedUsername.length > USERNAME_MAX_LENGTH) {
      showMessage(`Kullanıcı adı ${USERNAME_MAX_LENGTH} karakteri aşamaz.`);
      return;
    }
    // Emoji kontrolü
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
    if (emojiRegex.test(trimmedUsername)) {
      showMessage('Kullanıcı adı emoji içeremez.');
      return;
    }

    // Türkçe karakterler ve temel karakterler için kontrol (emoji hariç)
    if (!/^[a-zA-ZçğıöşüÇĞIİÖŞÜ0-9._]+$/.test(trimmedUsername)) {
      showMessage('Kullanıcı adı sadece harfler (ç,ğ,ı,ş,ü,ö dahil), rakamlar, noktalar ve alt çizgiler içerebilir.');
      return;
    }
    if (trimmedBio.length > BIO_MAX_LENGTH) {
      showMessage(`Biyografi ${BIO_MAX_LENGTH} karakteri aşamaz.`);
      return;
    }

    const isUsernameChanged = trimmedUsername !== originalUsername;
    let updatedUsernameFields = {};

    if (isUsernameChanged) {
      const now = Date.now();
      const lastChangeMs = typeof lastUsernameChange === 'number' ? lastUsernameChange : 0;
      const timeSinceLastChange = now - lastChangeMs;
      if (timeSinceLastChange < USERNAME_LOCK_PERIOD_MS && usernameChangeCount >= MAX_USERNAME_CHANGES_WITHIN_LOCK) {
        const daysLeft = Math.ceil((USERNAME_LOCK_PERIOD_MS - timeSinceLastChange) / (1000 * 60 * 60 * 24));
        showMessage(`Kullanıcı adınızı 15 gün içinde en fazla ${MAX_USERNAME_CHANGES_WITHIN_LOCK} kez değiştirebilirsiniz. Lütfen ${daysLeft} gün sonra tekrar deneyin.`);
        return;
      } else if (timeSinceLastChange < USERNAME_LOCK_PERIOD_MS) {
        updatedUsernameFields = {
          usernameChangeCount: usernameChangeCount + 1,
          lastUsernameChange: now,
        };
      } else {
        updatedUsernameFields = {
          usernameChangeCount: 1,
          lastUsernameChange: now,
        };
      }
      try {
        const q = query(collection(db, 'users'), where('username', '==', trimmedUsername));
        const qsnap = await getDocs(q);
        let taken = false;
        qsnap.forEach((docu) => {
          if (docu.id !== auth.currentUser.uid) {
            taken = true;
          }
        });
        if (taken) {
          showMessage('Bu kullanıcı adı zaten kullanılıyor.');
          return;
        }
      } catch (err) {
        showMessage(`Kullanıcı adı sorgusu başarısız: ${err.message}`);
        return;
      }
    }

    setSaving(true);
    try {
      const userRef = doc(db, 'users', auth.currentUser.uid);
      const updatedData = {
        username: isUsernameChanged ? trimmedUsername : originalUsername,
        bio: trimmedBio,
        gender: trimmedGender,
        avatarIndex,
        // profilePic: Sadece avatar seçilmişse kaydet, yoksa null
        profilePic: avatarIndex >= 0 ? `avatar${avatarIndex + 1}` : null,
        ...updatedUsernameFields,
      };
      await updateDoc(userRef, updatedData);
      showMessage('Profiliniz başarıyla güncellendi.');

      // Profil ekranına dönelim
      setTimeout(() => {
        navigation.navigate('MainTabs', { screen: 'Profil' });
      }, 1000); // Mesajı göstermek için 1 saniye bekleyelim
    } catch (err) {
      showMessage('Profiliniz güncellenirken bir hata oluştu.');
    } finally {
      setSaving(false);
    }
  }

  function renderAvatarItem({ item, index }) {
    const selected = index === avatarIndex;
    return (
      <TouchableOpacity
        style={[styles.avatarItemContainer, selected && styles.avatarItemSelected]}
        onPress={() => {
          // Eğer zaten seçiliyse seçimi kaldır (-1 yap), değilse seç
          if (selected) {
            setAvatarIndex(-1);
          } else {
            setAvatarIndex(index);
          }
        }}
      >
        <Image source={item} style={styles.avatarItemImage} />
        {selected && (
          <View style={styles.checkOverlay}>
            <Ionicons name="checkmark-circle" size={24} color="#1abc9c" />
          </View>
        )}
      </TouchableOpacity>
    );
  }

  function renderGenderOption(option) {
    const isSelected = gender === option;
    return (
      <TouchableOpacity
        style={[styles.genderOption, isSelected && styles.genderOptionSelected]}
        onPress={() => setGender(option)}
      >
        <Text style={[styles.genderText, isSelected && styles.genderTextSelected]}>{option}</Text>
      </TouchableOpacity>
    );
  }

  if (loadingProfile) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1abc9c" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1, backgroundColor: '#000' }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      {renderHeader()}
      <ScrollView style={styles.container} contentContainerStyle={{ paddingBottom: 50 }}>
        {/* Avatar Seçimi */}
        <Text style={styles.sectionLabel}>Avatar Seç</Text>
        <FlatList
          data={DUMMY_AVATARS}
          keyExtractor={(_, idx) => String(idx)}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 10, marginVertical: 10 }}
          renderItem={renderAvatarItem}
        />

        {/* Username */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Kullanıcı Adı</Text>
          <TextInput
            style={styles.input}
            value={username}
            onChangeText={(txt) => {
              // Emoji filtreleme
              const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
              const filteredText = txt.replace(emojiRegex, '');
              if (filteredText.length <= USERNAME_MAX_LENGTH) setUsername(filteredText);
            }}
            placeholder="harfler (ç,ğ,ı,ş,ü,ö dahil), rakamlar, _ ve . kullanılabilir"
            placeholderTextColor="#666"
            autoCapitalize="none"
          />
          <Text style={styles.charCount}>{username.length}/{USERNAME_MAX_LENGTH}</Text>
          <Text style={styles.usernameChangeInfo}>
            15 gün içinde en fazla {MAX_USERNAME_CHANGES_WITHIN_LOCK} defa kullanıcı adınızı değiştirebilirsiniz.
          </Text>
        </View>

        {/* Biography */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Biyografi (en fazla {BIO_MAX_LENGTH} karakter, 6 satır)</Text>
          <TextInput
            style={[styles.input, styles.bioInput]}
            value={bio}
            onChangeText={(value) => {
              // Karakter sınırı kontrolü
              if (value.length > BIO_MAX_LENGTH) return;

              // Satır sayısı kontrolü (en fazla 8 satır)
              const lines = value.split('\n');
              if (lines.length > 8) return;

              setBio(value);
            }}
            placeholder="Kendiniz hakkında kısa bir açıklama..."
            placeholderTextColor="#666"
            multiline
            maxLength={BIO_MAX_LENGTH}
            numberOfLines={8}
          />
          <Text style={styles.charCount}>
            {bio.length}/{BIO_MAX_LENGTH} karakter • {bio.split('\n').length}/8 satır
          </Text>
        </View>

        {/* Gender */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Cinsiyet</Text>
          <View style={styles.genderContainer}>
            {renderGenderOption("Erkek")}
            {renderGenderOption("Kadın")}
            {renderGenderOption("Diğer")}
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity style={styles.saveButton} onPress={handleSaveProfile} disabled={saving}>
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.saveButtonText}>Kaydet</Text>
          )}
        </TouchableOpacity>
      </ScrollView>

      <MessageModal
        visible={messageModalVisible}
        message={messageModalText}
        onClose={() => setMessageModalVisible(false)}
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#000',
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  headerBar: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    backgroundColor: '#000',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    paddingHorizontal: 10,
  },
  backButtonHeader: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  sectionLabel: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginTop: 10,
  },
  inputGroup: {
    marginTop: 12,
  },
  label: {
    color: '#aaa',
    marginBottom: 6,
    fontSize: 14,
  },
  input: {
    backgroundColor: '#1a1a1a',
    color: '#fff',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
  },
  bioInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  charCount: {
    color: '#666',
    fontSize: 12,
    textAlign: 'right',
    marginTop: 2,
  },
  usernameChangeInfo: {
    color: '#888',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'left',
  },
  genderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  genderOption: {
    backgroundColor: '#1a1a1a',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#666',
  },
  genderOptionSelected: {
    backgroundColor: '#0066CC',
    borderColor: '#1abc9c',
  },
  genderText: {
    color: '#fff',
    fontSize: 16,
  },
  genderTextSelected: {
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#0066CC',
    borderRadius: 6,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 40,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  avatarItemContainer: {
    marginRight: 10,
  },
  avatarItemImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 1,
    borderColor: '#444',
  },
  avatarItemSelected: {
    borderColor: '#1abc9c',
    borderWidth: 2,
    borderRadius: 40,
  },
  checkOverlay: {
    position: 'absolute',
    right: 2,
    bottom: 2,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    padding: 2,
  },
  msgOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  msgContainer: {
    backgroundColor: '#222',
    padding: 20,
    borderRadius: 10,
    width: '75%',
    alignItems: 'center',
  },
  msgText: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  msgButton: {
    backgroundColor: '#1DA1F2',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
  },
  msgButtonText: {
    color: '#fff',
    fontSize: 16,
  },
});

export default ProfileEditScreen;
