import { collection, addDoc, serverTimestamp, query, where, getDocs, deleteDoc, doc, getDoc } from 'firebase/firestore';
import { db } from '../firebase';

// Bildirim türleri
export const NOTIFICATION_TYPES = {
  LIKE: 'like',
  COMMENT: 'comment',
  FOLLOW: 'follow',
  FOLLOW_REQUEST: 'follow_request',
  FOLLOW_REQUEST_ACCEPTED: 'follow_request_accepted',
  MENTION: 'mention',
  REPLY: 'reply'
};

/**
 * Bildirim oluşturma fonksiyonu
 * @param {string} type - Bil<PERSON><PERSON> tür<PERSON> (NOTIFICATION_TYPES'dan bir değer)
 * @param {string} senderId - Bildirimi gönderen kullanıcının ID'si
 * @param {string} recipientId - Bildirimi alacak kullanıcının ID'si
 * @param {string} postId - <PERSON>l<PERSON><PERSON> gönderinin ID'si (varsa)
 * @param {string} commentId - <PERSON><PERSON><PERSON><PERSON> yo<PERSON> ID'si (varsa)
 * @param {string} replyId - <PERSON>l<PERSON>li cevabın ID'si (varsa)
 * @param {string} content - Bildirim içeriği (varsa)
 * @returns {Promise<string>} - Oluşturulan bildirimin ID'si
 */
export const createNotification = async (
  type,
  senderId,
  recipientId,
  postId = null,
  commentId = null,
  replyId = null,
  content = null
) => {
  // Kendine bildirim göndermeyi engelle
  if (senderId === recipientId) return null;

  // Kullanıcının bildirim ayarlarını kontrol et
  try {
    const recipientDoc = await getDoc(doc(db, 'users', recipientId));
    if (recipientDoc.exists()) {
      const recipientData = recipientDoc.data();

      // Eğer genel bildirimler kapalıysa hiç bildirim gönderme
      // isNotificationEnabled field'ı yoksa varsayılan olarak true kabul et
      const isNotificationEnabled = recipientData.isNotificationEnabled !== false;
      if (!isNotificationEnabled) {
        console.log('Bildirim gönderilmedi - kullanıcı genel bildirimleri kapatmış');
        return null;
      }

      const notificationSettings = recipientData.notificationSettings || {
        likes: true,
        comments: true,
        follows: true,
        mentions: true,
        replies: true
      };

      // Bildirim türüne göre ayarları kontrol et
      let settingKey;
      switch (type) {
        case NOTIFICATION_TYPES.LIKE:
          settingKey = 'likes';
          break;
        case NOTIFICATION_TYPES.COMMENT:
          settingKey = 'comments';
          break;
        case NOTIFICATION_TYPES.FOLLOW:
        case NOTIFICATION_TYPES.FOLLOW_REQUEST:
        case NOTIFICATION_TYPES.FOLLOW_REQUEST_ACCEPTED:
          settingKey = 'follows';
          break;
        case NOTIFICATION_TYPES.MENTION:
          settingKey = 'mentions';
          break;
        case NOTIFICATION_TYPES.REPLY:
          settingKey = 'replies';
          break;
        default:
          settingKey = null;
      }

      // Eğer kullanıcı bu tür bildirimleri kapatmışsa bildirim gönderme
      if (settingKey && !notificationSettings[settingKey]) {
        console.log(`Bildirim gönderilmedi - kullanıcı ${settingKey} bildirimlerini kapatmış`);
        return null;
      }
    }
  } catch (error) {
    console.error('Bildirim ayarları kontrol edilirken hata:', error);
    // Hata durumunda bildirimi gönder (varsayılan davranış)
  }
  
  try {
    // Aynı bildirimin daha önce gönderilip gönderilmediğini kontrol et
    const notificationsRef = collection(db, 'notifications');
    const q = query(
      notificationsRef,
      where('type', '==', type),
      where('senderId', '==', senderId),
      where('recipientId', '==', recipientId),
      where('postId', '==', postId)
    );
    
    if (type === NOTIFICATION_TYPES.LIKE) {
      // Beğeni bildirimi için aynı gönderi için önceki bildirimleri sil
      const existingDocs = await getDocs(q);
      existingDocs.forEach(async (doc) => {
        await deleteDoc(doc.ref);
      });
    } else if (type === NOTIFICATION_TYPES.FOLLOW) {
      // Takip bildirimi için aynı kullanıcıdan gelen önceki takip bildirimlerini sil
      const existingDocs = await getDocs(q);
      existingDocs.forEach(async (doc) => {
        await deleteDoc(doc.ref);
      });
    }
    
    // Yeni bildirimi oluştur
    const notificationData = {
      type,
      senderId,
      recipientId,
      read: false,
      createdAt: serverTimestamp()
    };
    
    // Opsiyonel alanları ekle
    if (postId) notificationData.postId = postId;
    if (commentId) notificationData.commentId = commentId;
    if (replyId) notificationData.replyId = replyId;
    if (content) notificationData.content = content;
    
    const docRef = await addDoc(notificationsRef, notificationData);
    return docRef.id;
  } catch (error) {
    console.error('Bildirim oluşturma hatası:', error);
    return null;
  }
};

/**
 * Bildirim silme fonksiyonu
 * @param {string} type - Bildirim türü
 * @param {string} senderId - Bildirimi gönderen kullanıcının ID'si
 * @param {string} recipientId - Bildirimi alacak kullanıcının ID'si
 * @param {string} postId - İlgili gönderinin ID'si (varsa)
 */
export const deleteNotification = async (type, senderId, recipientId, postId = null) => {
  try {
    const notificationsRef = collection(db, 'notifications');
    let q;
    
    if (postId) {
      q = query(
        notificationsRef,
        where('type', '==', type),
        where('senderId', '==', senderId),
        where('recipientId', '==', recipientId),
        where('postId', '==', postId)
      );
    } else {
      q = query(
        notificationsRef,
        where('type', '==', type),
        where('senderId', '==', senderId),
        where('recipientId', '==', recipientId)
      );
    }
    
    const snapshot = await getDocs(q);
    snapshot.forEach(async (doc) => {
      await deleteDoc(doc.ref);
    });
    
    return true;
  } catch (error) {
    console.error('Bildirim silme hatası:', error);
    return false;
  }
};
