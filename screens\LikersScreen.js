import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Image,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { doc, getDoc, updateDoc, arrayUnion, arrayRemove } from 'firebase/firestore';
import { db, auth } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { sendFollowRequest, getFollowRequest, cancelFollowRequest } from '../utils/followRequestUtils';
import { useToast } from '../contexts/ToastContext';
import { useFollowStatus } from '../contexts/FollowStatusContext';

// Avatar mapping
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

function LikersScreen() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [followingList, setFollowingList] = useState([]);
  const [blockedUsers, setBlockedUsers] = useState([]);

  const route = useRoute();
  const navigation = useNavigation();
  const { showToast } = useToast();
  const {
    hasPendingRequest,
    handleFollowRequestSent,
    handleFollowRequestCancelled
  } = useFollowStatus();
  // route.params.likedBy: [ 'uid1', 'uid2', ... ]
  const { likedBy } = route.params || {};

  useEffect(() => {
    if (!likedBy || !Array.isArray(likedBy)) {
      setUsers([]);
      setLoading(false);
      return;
    }
    loadCurrentUserData();
    loadLikers();
  }, [likedBy]);

  async function loadCurrentUserData() {
    try {
      const currentUserId = auth.currentUser?.uid;
      if (currentUserId) {
        const userDocRef = doc(db, 'users', currentUserId);
        const snap = await getDoc(userDocRef);
        if (snap.exists()) {
          const userData = snap.data();
          setCurrentUser(userData);
          setFollowingList(userData.following || []);
          setBlockedUsers(userData.blockedUsers || []);
        }
      }
    } catch (error) {
      console.log('Error loading current user data:', error);
    }
  }

  async function loadLikers() {
    setLoading(true);
    try {
      const userPromises = likedBy.map(async (uid) => {
        const userDocRef = doc(db, 'users', uid);
        const snap = await getDoc(userDocRef);
        if (snap.exists()) {
          return { id: uid, ...snap.data() };
        }
        return null;
      });
      const results = (await Promise.all(userPromises)).filter(u => u !== null);

      // Engellenen kullanıcıları filtrele
      const filteredResults = results.filter(user => !blockedUsers.includes(user.id));

      setUsers(filteredResults);
    } catch (error) {
      console.log('LikersScreen error:', error);
    }
    setLoading(false);
  }

  function onRefresh() {
    setRefreshing(true);
    Promise.all([loadCurrentUserData(), loadLikers()]).then(() => setRefreshing(false));
  }

  async function handleFollowToggle(targetUserId) {
    try {
      const currentUserId = auth.currentUser?.uid;
      if (!currentUserId) return;

      // Engelleme kontrolü
      if (blockedUsers.includes(targetUserId)) {
        showToast({
          message: 'Engellediğiniz kullanıcıyı takip edemezsiniz.',
          type: 'error'
        });
        return;
      }

      const isFollowing = followingList.includes(targetUserId);

      if (isFollowing) {
        // Takibi bırak
        const currentUserRef = doc(db, 'users', currentUserId);
        const targetUserRef = doc(db, 'users', targetUserId);

        await updateDoc(currentUserRef, {
          following: arrayRemove(targetUserId)
        });
        await updateDoc(targetUserRef, {
          followers: arrayRemove(currentUserId)
        });
        setFollowingList(prev => prev.filter(id => id !== targetUserId));

        // Global state'i güncelle
        handleUnfollowSuccess(targetUserId);
      } else {
        // Takip etmeden önce hedef kullanıcının gizli hesap olup olmadığını kontrol et
        const targetUserDoc = await getDoc(doc(db, 'users', targetUserId));
        if (!targetUserDoc.exists()) return;

        const targetUserData = targetUserDoc.data();
        const isPrivateAccount = targetUserData.isPrivateAccount || false;

        // Hedef kullanıcının bizi engellemiş olup olmadığını kontrol et
        const targetUserBlockedUsers = targetUserData.blockedUsers || [];
        if (targetUserBlockedUsers.includes(currentUserId)) {
          showToast({
            message: 'Bu kullanıcı sizi engellediği için takip edemezsiniz.',
            type: 'error'
          });
          return;
        }

        if (isPrivateAccount) {
          // Gizli hesap - takip isteği gönder
          const existingRequest = await getFollowRequest(currentUserId, targetUserId);
          if (existingRequest) {
            showToast({
              message: 'Bu kullanıcıya zaten takip isteği gönderilmiş.',
              type: 'info'
            });
            return;
          }

          const success = await sendFollowRequest(currentUserId, targetUserId);
          if (success) {
            handleFollowRequestSent(targetUserId);
            showToast({
              message: 'İstek gönderildi',
              type: 'success'
            });
          } else {
            showToast({
              message: 'Takip isteği gönderilemedi.',
              type: 'error'
            });
          }
        } else {
          // Açık hesap - direkt takip et
          const currentUserRef = doc(db, 'users', currentUserId);
          const targetUserRef = doc(db, 'users', targetUserId);

          await updateDoc(currentUserRef, {
            following: arrayUnion(targetUserId)
          });
          await updateDoc(targetUserRef, {
            followers: arrayUnion(currentUserId)
          });
          setFollowingList(prev => [...prev, targetUserId]);

          // Global state'i güncelle
          handleFollowSuccess(targetUserId);
        }
      }
    } catch (error) {
      console.log('Error toggling follow:', error);
      showToast({
        message: 'Takip işlemi sırasında bir hata oluştu.',
        type: 'error'
      });
    }
  }

  // Takip isteğini iptal et
  async function handleCancelFollowRequest(targetUserId) {
    try {
      const currentUserId = auth.currentUser?.uid;
      if (!currentUserId) return;

      const success = await cancelFollowRequest(currentUserId, targetUserId);
      if (success) {
        handleFollowRequestCancelled(targetUserId);
        showToast({
          message: 'Takip isteği iptal edildi',
          type: 'success'
        });
      } else {
        showToast({
          message: 'İstek iptal edilirken hata oluştu',
          type: 'error'
        });
      }
    } catch (error) {
      console.error('Takip isteği iptal etme hatası:', error);
      showToast({
        message: 'Bir hata oluştu',
        type: 'error'
      });
    }
  }

  function renderItem({ item }) {
    const currentUserId = auth.currentUser?.uid;
    const isCurrentUser = item.id === currentUserId;
    const isFollowing = followingList.includes(item.id);

    // Avatar seçim mantığı
    const hasRealPhoto = item.photoURL &&
                        item.photoURL.trim() !== '' &&
                        item.photoURL !== 'null' &&
                        item.photoURL !== 'undefined';

    const hasSelectedAvatar = item.profilePic &&
                             item.profilePic !== 'null' &&
                             item.profilePic !== 'undefined' &&
                             avatarMap[item.profilePic];

    let avatarSource;
    if (hasRealPhoto) {
      avatarSource = { uri: item.photoURL };
    } else if (hasSelectedAvatar) {
      avatarSource = avatarMap[item.profilePic];
    } else {
      avatarSource = require('../assets/default-avatar.png');
    }

    return (
      <View style={styles.userRow}>
        <TouchableOpacity
          style={styles.userInfo}
          onPress={() => navigation.navigate('OtherProfile', { uid: item.id })}
        >
          <Image source={avatarSource} style={styles.avatar} />
          <Text style={styles.username}>{item.username || 'Kullanıcı'}</Text>
        </TouchableOpacity>

        {!isCurrentUser && (
          <TouchableOpacity
            style={[styles.followButton, isFollowing && styles.followingButton]}
            onPress={() => {
              if (isFollowing) {
                handleFollowToggle(item.id);
              } else if (hasPendingRequest(item.id)) {
                handleCancelFollowRequest(item.id);
              } else {
                handleFollowToggle(item.id);
              }
            }}
          >
            <Text style={[styles.followButtonText, isFollowing && styles.followingButtonText]}>
              {isFollowing
                ? 'Takip Ediliyor'
                : hasPendingRequest(item.id)
                  ? 'İstek Gönderildi'
                  : 'Takip Et'
              }
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#8e44ad" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Üst Bar */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Beğenenler</Text>
        <View style={styles.rightSpace} />
      </View>

      {users.length === 0 ? (
        <Text style={styles.noLikesText}>Henüz beğenen yok.</Text>
      ) : (
        <FlatList
          data={users}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />
          }
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    borderBottomWidth: 0.5,
    borderBottomColor: '#333',
    paddingHorizontal: 10,
    backgroundColor: '#000',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  rightSpace: {
    width: 40,
  },
  loaderContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noLikesText: {
    color: '#aaa',
    fontSize: 15,
    textAlign: 'center',
    marginTop: 30,
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#333',
    borderBottomWidth: 1,
    padding: 15,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#666',
  },
  username: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
  followButton: {
    backgroundColor: '#1abc9c',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 100,
    alignItems: 'center',
  },
  followingButton: {
    backgroundColor: '#333',
    borderWidth: 1,
    borderColor: '#666',
  },
  followButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  followingButtonText: {
    color: '#aaa',
  },
});

export default LikersScreen;
