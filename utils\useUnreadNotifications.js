import { useState, useEffect } from 'react';
import { collection, query, where, onSnapshot } from 'firebase/firestore';
import { db, auth } from '../firebase';

/**
 * Okunmamış bildirim sayısını takip eden hook
 * @returns {number} unreadCount - Okunmamış bildirim sayısı
 */
export const useUnreadNotifications = () => {
  const [unreadCount, setUnreadCount] = useState(0);
  const currentUser = auth.currentUser;

  useEffect(() => {
    if (!currentUser) {
      setUnreadCount(0);
      return;
    }

    // Okunmamış bildirimleri gerçek zamanlı dinle
    const notificationsRef = collection(db, 'notifications');
    const q = query(
      notificationsRef,
      where('recipientId', '==', currentUser.uid),
      where('read', '==', false)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const count = snapshot.size;
      setUnreadCount(count);
      
      // Debug log
      console.log(`<PERSON>unmamış bildirim sayısı: ${count}`);
    }, (error) => {
      console.error('Bildirim dinleme hatası:', error);
      setUnreadCount(0);
    });

    return () => unsubscribe();
  }, [currentUser]);

  return unreadCount;
};
