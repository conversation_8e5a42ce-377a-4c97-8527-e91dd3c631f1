import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
  Animated,
  Modal,
  Pressable,
  SafeAreaView,
  StatusBar,
  Platform,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  collection,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
  addDoc,
  deleteDoc,
  query,
  where,
  onSnapshot
} from 'firebase/firestore';
import { auth, db } from '../firebase';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useToast } from '../contexts/ToastContext';
import { useNavigation } from '@react-navigation/native';
import { avatarMap } from '../utils/avatarMap';
import PostContent from '../components/PostContent';
import { createNotification, NOTIFICATION_TYPES } from '../utils/notificationUtils';
import { updateUserPopularity, listenToPostComments } from '../utils/popularityUtils';
import { sendFollowRequest, getFollowRequest } from '../utils/followRequestUtils';


// Yardımcı: "x süre önce" formatı
function timeAgo(timestamp, currentTime) {
  if (!timestamp || !timestamp.toDate) return 'Bilinmeyen Tarih';
  const postTime = timestamp.toDate().getTime();
  const diff = Math.floor((currentTime - postTime) / 1000);
  if (diff < 0) return '0 saniye önce';
  if (diff < 60) return diff + ' saniye önce';
  if (diff < 3600) return Math.floor(diff / 60) + ' dakika önce';
  if (diff < 86400) return Math.floor(diff / 3600) + ' saat önce';
  return Math.floor(diff / 86400) + ' gün önce';
}

const SavedPostsScreen = () => {
  const { theme } = useTheme();
  const { translations } = useLanguage();
  const { showToast } = useToast();
  const navigation = useNavigation();
  const [savedPosts, setSavedPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const currentUser = auth.currentUser;

  // FeedScreen'deki state'ler
  const [commentCounts, setCommentCounts] = useState({});
  const [followingIds, setFollowingIds] = useState([]);
  const [savedPostIds, setSavedPostIds] = useState([]);
  const [userData, setUserData] = useState(null);
  const [timeNow, setTimeNow] = useState(Date.now());
  const [blockedUsers, setBlockedUsers] = useState([]);

  // Beğeni animasyonu
  const likeAnimValues = useRef({});
  const [processingLikes, setProcessingLikes] = useState({});

  // Çift tıklama için
  const lastTapRef = useRef({});
  const doubleTapDelayMs = 300;

  // Modal state'leri
  const [optionsModalVisible, setOptionsModalVisible] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState(null);
  const [selectedPostOwnerId, setSelectedPostOwnerId] = useState(null);
  const [likesModalVisible, setLikesModalVisible] = useState(false);
  const [likesModalData, setLikesModalData] = useState([]);

  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      if (isMounted) {
        await loadSavedPosts();
      }
    };

    loadData();

    // Zaman güncelleyici
    const timeInterval = setInterval(() => {
      if (isMounted) {
        setTimeNow(Date.now());
      }
    }, 60000); // Her dakika güncelle

    return () => {
      isMounted = false;
      clearInterval(timeInterval);
    };
  }, []);

  // Kullanıcı verilerini yükle
  useEffect(() => {
    if (!currentUser) return;

    const loadUserData = async () => {
      try {
        const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
        if (userDoc.exists()) {
          setUserData(userDoc.data());
        }
      } catch (error) {
        console.error('Kullanıcı verisi yükleme hatası:', error);
      }
    };

    const loadFollowingIds = async () => {
      try {
        const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          setFollowingIds(userData.following || []);
          setBlockedUsers(userData.blockedUsers || []);
        }
      } catch (error) {
        console.error('Takip listesi yükleme hatası:', error);
      }
    };

    const loadSavedPostIds = async () => {
      try {
        const savedRef = collection(db, 'savedPosts');
        const q = query(savedRef, where('userId', '==', currentUser.uid));
        const snapshot = await getDocs(q);
        const ids = snapshot.docs.map(doc => doc.data().postId);
        setSavedPostIds(ids);
      } catch (error) {
        console.error('Kaydedilen gönderi ID\'leri yükleme hatası:', error);
      }
    };

    loadUserData();
    loadFollowingIds();
    loadSavedPostIds();
  }, [currentUser]);

  // Kaydedilen gönderileri yükle
  const loadSavedPosts = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      // Kullanıcının kaydettiği gönderileri al
      const savedRef = collection(db, 'savedPosts');
      const q = query(savedRef, where('userId', '==', currentUser.uid));
      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        setSavedPosts([]);
        setLoading(false);
        return;
      }

      // Kaydedilen gönderi ID'lerini al
      const postIds = snapshot.docs.map(doc => doc.data().postId);
      
      // Gönderileri al
      let savedPostsData = [];
      for (const postId of postIds) {
        try {
          const postDoc = await getDoc(doc(db, 'posts', postId));
          if (postDoc.exists()) {
            const postData = postDoc.data();
            savedPostsData.push({
              id: postDoc.id,
              ...postData,
              likedBy: postData.likedBy || [],
              likes: postData.likes || 0
            });
          }
        } catch (error) {
          console.error('Gönderi yükleme hatası:', error);
        }
      }

      // Kullanıcı ID'lerini topla (kendi kullanıcımızı da dahil et)
      const userIds = new Set();
      savedPostsData.forEach(post => {
        if (post.uid) {
          userIds.add(post.uid);
        }
      });

      // Kullanıcı verilerini paralel olarak yükle
      const userCache = {};
      await Promise.all(Array.from(userIds).map(async (userId) => {
        try {
          const userDoc = await getDoc(doc(db, 'users', userId));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            userCache[userId] = userData;
          } else {
            console.log(`Kullanıcı dokümanı bulunamadı: ${userId}`);
          }
        } catch (err) {
          console.error('Kullanıcı verisi yükleme hatası:', userId, err);
        }
      }));

      // Gönderi verilerini kullanıcı bilgileriyle birleştir (FeedScreen tarzı)
      savedPostsData = savedPostsData.filter(post => {
        if (post.uid && userCache[post.uid]) {
          const userData = userCache[post.uid];
          
          // Gönderi verilerini güncelle (FeedScreen'deki gibi Object.assign kullan)
          Object.assign(post, {
            profilePic: userData.profilePic || null,
            username: userData.username || 'Misafir',
            popularity: userData.popularity || 0,
            isPrivateAccount: userData.isPrivateAccount || false
          });
        } else {
          // Kullanıcı verisi bulunamazsa varsayılan değerler ata
          Object.assign(post, {
            profilePic: null,
            username: 'Misafir',
            popularity: 0,
            isPrivateAccount: false
          });
        }
        return true;
      });

      // Tarihe göre sırala (en yeni önce)
      savedPostsData.sort((a, b) => {
        const aTime = a.createdAt?.toDate?.() || new Date(0);
        const bTime = b.createdAt?.toDate?.() || new Date(0);
        return bTime - aTime;
      });

      // Engellenen kullanıcıları filtrele
      const filteredPosts = savedPostsData.filter(post => !blockedUsers.includes(post.uid));
      setSavedPosts(filteredPosts);

      // Yorum sayılarını yükle
      const commentCountsData = {};
      for (const post of savedPostsData) {
        try {
          const commentsRef = collection(db, 'posts', post.id, 'comments');
          const commentsSnapshot = await getDocs(commentsRef);
          commentCountsData[post.id] = commentsSnapshot.size;
        } catch (error) {
          console.error('Yorum sayısı yüklenirken hata:', error);
          commentCountsData[post.id] = 0;
        }
      }
      setCommentCounts(commentCountsData);

    } catch (error) {
      console.error('Kaydedilen gönderiler yüklenirken hata:', error);
      showToast({
        message: 'Kaydedilen gönderiler yüklenirken bir hata oluştu.',
        type: 'error'
      });
    }
    setLoading(false);
  };

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    loadSavedPosts().then(() => setRefreshing(false));
  };

  // Çift tıklama ile beğenme fonksiyonu
  const handleDoubleTap = (postId) => {
    if (processingLikes[postId]) return;
    if (!currentUser) return;

    const now = Date.now();
    const lastTap = lastTapRef.current[postId] || 0;
    lastTapRef.current[postId] = now;

    if (now - lastTap < doubleTapDelayMs) {
      const post = savedPosts.find(p => p.id === postId);
      if (!post) return;
      toggleLike(postId);
    }
  };

  // Beğeni işlemi
  function toggleLike(postId) {
    if (!currentUser) return;
    if (processingLikes[postId]) return;

    setProcessingLikes(prev => ({ ...prev, [postId]: true }));

    if (!likeAnimValues.current[postId]) {
      likeAnimValues.current[postId] = new Animated.Value(1);
    }

    const post = savedPosts.find(p => p.id === postId);
    if (!post) {
      setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      return;
    }

    const postRef = doc(db, 'posts', postId);

    getDoc(postRef).then(snap => {
      if (!snap.exists()) {
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        return;
      }

      const data = snap.data();
      const serverIsLiked = data.likedBy?.includes(currentUser.uid);
      const finalIsLiked = serverIsLiked;
      const newLikes = finalIsLiked ? Math.max(0, data.likes - 1) : (data.likes || 0) + 1;

      setSavedPosts(prev => {
        return prev.map(p => {
          if (p.id === postId) {
            let updatedLikedBy;

            if (finalIsLiked) {
              updatedLikedBy = p.likedBy?.filter(uid => uid !== currentUser.uid) || [];

              Animated.sequence([
                Animated.spring(likeAnimValues.current[postId], {
                  toValue: 0.8,
                  friction: 3,
                  tension: 100,
                  useNativeDriver: true
                }),
                Animated.spring(likeAnimValues.current[postId], {
                  toValue: 1,
                  friction: 3,
                  tension: 80,
                  useNativeDriver: true
                })
              ]).start();
            } else {
              updatedLikedBy = [...(p.likedBy || []), currentUser.uid];

              Animated.sequence([
                Animated.spring(likeAnimValues.current[postId], {
                  toValue: 1.3,
                  friction: 2,
                  tension: 120,
                  useNativeDriver: true
                }),
                Animated.spring(likeAnimValues.current[postId], {
                  toValue: 1,
                  friction: 3,
                  tension: 80,
                  useNativeDriver: true
                })
              ]).start();
            }

            return {
              ...p,
              likes: newLikes,
              likedBy: updatedLikedBy
            };
          }
          return p;
        });
      });

      // Firestore güncelleme
      updateDoc(postRef, {
        likes: newLikes,
        likedBy: finalIsLiked
          ? arrayRemove(currentUser.uid)
          : arrayUnion(currentUser.uid),
        lastLikeUpdate: serverTimestamp()
      }).then(() => {
        if (!finalIsLiked && data.uid !== currentUser.uid) {
          createNotification(
            NOTIFICATION_TYPES.LIKE,
            currentUser.uid,
            data.uid,
            postId
          );
        }

        if (data.uid) {
          return updateUserPopularity(data.uid, true);
        }
      }).catch(() => {
        // Hata durumunda
      }).finally(() => {
        setTimeout(() => {
          setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        }, 500);
      });
    }).catch(() => {
      setTimeout(() => {
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      }, 500);
    });
  }

  function handleCommentPress(postId) {
    navigation.navigate('Comments', { postId });
  }

  // Takip et/takipten çık
  async function handleToggleFollow(targetUid) {
    if (!currentUser || !targetUid || targetUid === currentUser.uid) return;

    // Engelleme kontrolü
    if (blockedUsers.includes(targetUid)) {
      showToast({
        message: 'Engellediğiniz kullanıcıyı takip edemezsiniz.',
        type: 'error'
      });
      return;
    }

    try {
      const isFollowing = followingIds.includes(targetUid);

      if (isFollowing) {
        // Takipten çık
        const userRef = doc(db, 'users', currentUser.uid);
        await updateDoc(userRef, {
          following: arrayRemove(targetUid)
        });

        const targetRef = doc(db, 'users', targetUid);
        await updateDoc(targetRef, {
          followers: arrayRemove(currentUser.uid)
        });

        setFollowingIds(prev => prev.filter(id => id !== targetUid));
        showToast({
          message: 'Takipten çıkıldı.',
          type: 'success'
        });
      } else {
        // Hedef kullanıcının gizli hesap olup olmadığını kontrol et
        const targetUserDoc = await getDoc(doc(db, 'users', targetUid));
        if (!targetUserDoc.exists()) return;

        const targetUserData = targetUserDoc.data();
        const isPrivateAccount = targetUserData.isPrivateAccount || false;

        // Hedef kullanıcının bizi engellemiş olup olmadığını kontrol et
        const targetUserBlockedUsers = targetUserData.blockedUsers || [];
        if (targetUserBlockedUsers.includes(currentUser.uid)) {
          showToast({
            message: 'Bu kullanıcı sizi engellediği için takip edemezsiniz.',
            type: 'error'
          });
          return;
        }

        if (isPrivateAccount) {
          // Gizli hesap - takip isteği gönder
          const existingRequest = await getFollowRequest(currentUser.uid, targetUid);
          if (existingRequest) {
            showToast({
              message: 'Bu kullanıcıya zaten takip isteği gönderilmiş.',
              type: 'info'
            });
            return;
          }

          const success = await sendFollowRequest(currentUser.uid, targetUid);
          if (success) {
            showToast({
              message: 'Takip isteği gönderildi. Kullanıcı isteğinizi kabul ettiğinde takip edeceksiniz.',
              type: 'success'
            });
          } else {
            showToast({
              message: 'Takip isteği gönderilemedi.',
              type: 'error'
            });
          }
        } else {
          // Açık hesap - direkt takip et
          const userRef = doc(db, 'users', currentUser.uid);
          await updateDoc(userRef, {
            following: arrayUnion(targetUid)
          });

          const targetRef = doc(db, 'users', targetUid);
          await updateDoc(targetRef, {
            followers: arrayUnion(currentUser.uid)
          });

          setFollowingIds(prev => [...prev, targetUid]);
          showToast({
            message: 'Takip edildi.',
            type: 'success'
          });

          // Takip bildirimi oluştur
          createNotification(
            NOTIFICATION_TYPES.FOLLOW,
            currentUser.uid,
            targetUid
          );
        }
      }
    } catch (error) {
      console.error('Takip işlemi hatası:', error);
      showToast({
        message: 'Bir hata oluştu',
        type: 'error'
      });
    }
  }

  function openOptionsModal(postId, ownerId) {
    setSelectedPostId(postId);
    setSelectedPostOwnerId(ownerId);
    setOptionsModalVisible(true);
  }

  function closeOptionsModal() {
    setOptionsModalVisible(false);
    setSelectedPostId(null);
    setSelectedPostOwnerId(null);
  }

  // Beğenenleri göster
  function handleShowLikes(likedBy) {
    if (!likedBy || likedBy.length === 0) return;

    setLikesModalData(likedBy);
    setLikesModalVisible(true);
  }

  // Gönderi bildirme
  async function handleReportPost() {
    if (!currentUser || !selectedPostId) return;

    try {
      await addDoc(collection(db, 'reports'), {
        postId: selectedPostId,
        reportedBy: currentUser.uid,
        reason: 'inappropriate',
        timestamp: serverTimestamp(),
        status: 'pending'
      });

      showToast({
        message: 'Gönderi bildirildi. İnceleme sonrası gerekli işlemler yapılacaktır.',
        type: 'success'
      });
    } catch (error) {
      console.error('Bildirim hatası:', error);
      showToast({
        message: 'Bildirim gönderilirken bir hata oluştu.',
        type: 'error'
      });
    }
  }

  // Kullanıcı engelleme
  async function handleBlockUser(targetUid) {
    if (!currentUser || !targetUid || targetUid === currentUser.uid) return;

    try {
      const userRef = doc(db, 'users', currentUser.uid);
      const targetUserRef = doc(db, 'users', targetUid);

      // Engellenen kullanıcılar listesine ekle
      await updateDoc(userRef, {
        blockedUsers: arrayUnion(targetUid)
      });

      // Takip ilişkilerini kaldır
      // 1. Eğer ben onu takip ediyorsam, takipten çık
      if (followingIds.includes(targetUid)) {
        await updateDoc(userRef, {
          following: arrayRemove(targetUid)
        });
        await updateDoc(targetUserRef, {
          followers: arrayRemove(currentUser.uid)
        });

        // follows koleksiyonundan da sil
        const followQuery = query(
          collection(db, 'follows'),
          where('followerUid', '==', currentUser.uid),
          where('followingUid', '==', targetUid)
        );
        const followDocs = await getDocs(followQuery);
        followDocs.forEach(async (doc) => {
          await deleteDoc(doc.ref);
        });

        // UI'ı güncelle
        setFollowingIds(prev => prev.filter(id => id !== targetUid));
      }

      // 2. Eğer o beni takip ediyorsa, onu takipçilerimden çıkar
      const userDoc = await getDoc(userRef);
      if (userDoc.exists()) {
        const userData = userDoc.data();
        const myFollowers = userData.followers || [];

        if (myFollowers.includes(targetUid)) {
          await updateDoc(userRef, {
            followers: arrayRemove(targetUid)
          });
          await updateDoc(targetUserRef, {
            following: arrayRemove(currentUser.uid)
          });

          // follows koleksiyonundan da sil
          const reverseFollowQuery = query(
            collection(db, 'follows'),
            where('followerUid', '==', targetUid),
            where('followingUid', '==', currentUser.uid)
          );
          const reverseFollowDocs = await getDocs(reverseFollowQuery);
          reverseFollowDocs.forEach(async (doc) => {
            await deleteDoc(doc.ref);
          });
        }
      }

      // Engellenen kullanıcının gönderilerini listeden kaldır
      setSavedPosts(prev => prev.filter(post => post.uid !== targetUid));

      showToast({
        message: 'Kullanıcı engellendi.',
        type: 'success'
      });
    } catch (error) {
      console.error('Engelleme hatası:', error);
      showToast({
        message: 'Bir hata oluştu.',
        type: 'error'
      });
    }
  }

  // Gönderiyi kaydetme/kaydetmekten çıkarma
  async function handleSavePost(postId) {
    if (!currentUser) return;

    try {
      const savedRef = collection(db, 'savedPosts');
      const q = query(savedRef, where('userId', '==', currentUser.uid), where('postId', '==', postId));
      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        // Kaydet
        await addDoc(savedRef, {
          userId: currentUser.uid,
          postId: postId,
          savedAt: serverTimestamp()
        });

        setSavedPostIds(prev => [...prev, postId]);
        showToast({
          message: 'Gönderi kaydedildi.',
          type: 'success'
        });
      } else {
        // Kaydetmekten çıkar
        const docToDelete = snapshot.docs[0];
        await deleteDoc(doc(db, 'savedPosts', docToDelete.id));

        setSavedPostIds(prev => prev.filter(id => id !== postId));
        setSavedPosts(prev => prev.filter(post => post.id !== postId));
        showToast({
          message: 'Gönderi kaydetmekten çıkarıldı.',
          type: 'success'
        });
      }
    } catch (error) {
      console.error('Kaydetme hatası:', error);
      showToast({
        message: 'Bir hata oluştu.',
        type: 'error'
      });
    }
  }

  // Gönderi silme
  async function handleDeletePost() {
    if (!currentUser || !selectedPostId) return;

    try {
      await deleteDoc(doc(db, 'posts', selectedPostId));
      setSavedPosts(prev => prev.filter(post => post.id !== selectedPostId));

      showToast({
        message: 'Gönderi silindi.',
        type: 'success'
      });
    } catch (error) {
      console.error('Silme hatası:', error);
      showToast({
        message: 'Gönderi silinirken bir hata oluştu.',
        type: 'error'
      });
    }
  }

  // Gönderi render fonksiyonu
  const renderPostItem = ({ item }) => {
    // Animasyon değerini başlat
    if (!likeAnimValues.current[item.id]) {
      likeAnimValues.current[item.id] = new Animated.Value(1);
    }

    const displayName = (item.username && item.username.trim() !== '') ? item.username : 'Misafir';
    const isLiked = (item.likedBy || []).includes(currentUser?.uid ?? '');
    const commentCount = commentCounts[item.id] || 0;
    const isMyPost = item.uid === currentUser?.uid;
    const isFollowing = followingIds.includes(item.uid);
    const isSaved = savedPostIds.includes(item.id);

    // Profil fotoğrafı için güvenli kaynak belirleme
    const getAvatarSource = () => {
      try {
        if (isMyPost && userData && userData.avatarIndex !== undefined) {
          const avatarKey = 'avatar' + (userData.avatarIndex + 1);
          return avatarMap[avatarKey] || require('../assets/default-avatar.png');
        }
        if (item.profilePic && avatarMap[item.profilePic]) {
          return avatarMap[item.profilePic];
        }
        return require('../assets/default-avatar.png');
      } catch (error) {
        console.error('Avatar yükleme hatası:', error);
        return require('../assets/default-avatar.png');
      }
    };

    // Kullanıcı rütbesi (LikedPostsScreen'deki gibi)
    const userRank =
      item.popularity >= 1000 ? 'Efsane' :
      item.popularity >= 500 ? 'Yıldız' :
      item.popularity >= 200 ? 'Popüler' :
      item.popularity >= 100 ? 'Yükselen' :
      item.popularity >= 50 ? 'Aktif' :
      item.popularity >= 20 ? 'Başlangıç' :
      item.popularity > 0 ? 'Yeni Üye' : '';

    return (
      <View style={styles.card}>
        <View style={styles.postHeader}>
          <View style={styles.headerLeft}>
            <TouchableOpacity
              onPress={() => {
                requestAnimationFrame(() => {
                  if (item.uid === currentUser?.uid) {
                    navigation.navigate('MainTabs', { screen: 'Profil' });
                  } else {
                    navigation.navigate('OtherProfile', { uid: item.uid });
                  }
                });
              }}
              activeOpacity={0.7}
            >
              <Image
                source={getAvatarSource()}
                style={styles.avatar}
                onError={() => console.log('Avatar yükleme hatası')}
              />
            </TouchableOpacity>
            <View style={styles.userInfoColumn}>
              <View style={styles.userRow}>
                <TouchableOpacity
                  onPress={() => {
                    requestAnimationFrame(() => {
                      if (item.uid === currentUser?.uid) {
                        navigation.navigate('MainTabs', { screen: 'Profil' });
                      } else {
                        navigation.navigate('OtherProfile', { uid: item.uid });
                      }
                    });
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.username}>{displayName}</Text>
                  <Text style={styles.userRank}>{userRank || 'Yeni Üye'}</Text>
                </TouchableOpacity>
                {!isMyPost && (
                  <TouchableOpacity
                    style={[styles.followButton, { marginLeft: 8 }]}
                    onPress={() => handleToggleFollow(item.uid)}
                  >
                    <Text style={styles.followButtonText}>
                      {isFollowing ? 'Takipten Çık' : 'Takip Et'}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>
          <View style={styles.headerRight}>
            <Text style={styles.postTypeLabel}>
              {item.type === 'Şarkı Sözü'
                ? 'Şarkı'
                : item.type === 'Şiir'
                  ? 'Şiir'
                  : item.type}
            </Text>
            <TouchableOpacity
              style={styles.optionsButton}
              onPress={() => openOptionsModal(item.id, item.uid)}
            >
              <Ionicons name="ellipsis-horizontal" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
        <TouchableOpacity
          style={styles.cardContent}
          activeOpacity={0.9}
          onPress={() => handleDoubleTap(item.id)}
        >
          <PostContent content={item.content} maxLength={150} style={styles.content} />
        </TouchableOpacity>
        <View style={styles.cardFooter}>
          <View style={styles.footerLeft}>
            <TouchableOpacity
              style={styles.likeContainer}
              onPress={() => toggleLike(item.id)}
              onLongPress={() => handleShowLikes(item.likedBy)}
              disabled={processingLikes[item.id]}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Animated.View style={{ transform: [{ scale: likeAnimValues.current[item.id] || new Animated.Value(1) }] }}>
                <Ionicons
                  name={isLiked ? 'heart' : 'heart-outline'}
                  size={24}
                  color={isLiked ? '#e74c3c' : '#fff'}
                />
              </Animated.View>
              <Text style={[styles.actionText, { marginLeft: 6 }]}>{item.likes}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { marginLeft: -2 }]}
              onPress={() => handleCommentPress(item.id)}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Ionicons name="chatbubble-outline" size={24} color="#fff" />
              <Text style={styles.actionText}>Yorum ({commentCount})</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { marginLeft: 10 }]}
              onPress={() => handleSavePost(item.id)}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Ionicons
                name={savedPostIds.includes(item.id) ? "bookmark" : "bookmark-outline"}
                size={22}
                color="#fff"
              />
              <Text style={styles.actionText}>{savedPostIds.includes(item.id) ? "Kaydedildi" : "Kaydet"}</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.timestampFooter}>
            {timeAgo(item.createdAt, timeNow)}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={theme.primary}
        translucent={false}
      />
      <View style={[styles.header, { backgroundColor: theme.primary }]}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Kaydedilen Gönderiler</Text>
      </View>

      {loading ? (
        <ActivityIndicator size="large" color={theme.primary} style={styles.loader} />
      ) : savedPosts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="bookmark-outline" size={64} color={theme.text} />
          <Text style={[styles.emptyText, { color: theme.text }]}>
            Henüz kaydedilen gönderi bulunmamaktadır
          </Text>
        </View>
      ) : (
        <FlatList
          data={savedPosts}
          renderItem={renderPostItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={theme.text}
            />
          }
          contentContainerStyle={styles.listContainer}
          removeClippedSubviews={true}
          maxToRenderPerBatch={5}
          updateCellsBatchingPeriod={100}
          initialNumToRender={3}
          windowSize={10}
          getItemLayout={(data, index) => ({
            length: 200, // Ortalama gönderi yüksekliği
            offset: 200 * index,
            index,
          })}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Beğeniler Modal */}
      {likesModalVisible && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={likesModalVisible}
          onRequestClose={() => setLikesModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Beğenenler</Text>
                <TouchableOpacity
                  onPress={() => setLikesModalVisible(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              <FlatList
                data={likesModalData}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item: userId }) => (
                  <LikeUserItem userId={userId} />
                )}
                style={styles.modalList}
              />
            </View>
          </View>
        </Modal>
      )}

      {/* Seçenekler Modal */}
      {optionsModalVisible && (
        <Modal
          animationType="fade"
          transparent={true}
          visible={optionsModalVisible}
          onRequestClose={closeOptionsModal}
        >
          <Pressable style={styles.modalOverlay} onPress={closeOptionsModal}>
            <View style={styles.optionsModal}>
              {selectedPostOwnerId === currentUser?.uid ? (
                <TouchableOpacity
                  style={styles.optionButton}
                  onPress={() => {
                    closeOptionsModal();
                    handleDeletePost();
                  }}
                >
                  <Ionicons name="trash-outline" size={20} color="#e74c3c" />
                  <Text style={[styles.optionText, { color: '#e74c3c' }]}>Sil</Text>
                </TouchableOpacity>
              ) : (
                <>
                  <TouchableOpacity
                    style={styles.optionButton}
                    onPress={() => {
                      closeOptionsModal();
                      handleReportPost();
                    }}
                  >
                    <Ionicons name="flag-outline" size={20} color="#fff" />
                    <Text style={styles.optionText}>Bildir</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.optionButton}
                    onPress={() => {
                      closeOptionsModal();
                      handleBlockUser(selectedPostOwnerId);
                    }}
                  >
                    <Ionicons name="ban-outline" size={20} color="#e74c3c" />
                    <Text style={[styles.optionText, { color: '#e74c3c' }]}>Engelle</Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </Pressable>
        </Modal>
      )}
    </SafeAreaView>
  );
};

// Beğenen kullanıcı item'ı
const LikeUserItem = ({ userId }) => {
  const [userInfo, setUserInfo] = useState(null);
  const navigation = useNavigation();
  const currentUser = auth.currentUser;

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          setUserInfo(userSnap.data());
        }
      } catch (error) {
        console.error('Kullanıcı bilgisi alınırken hata:', error);
      }
    };

    fetchUserInfo();
  }, [userId]);

  if (!userInfo) return null;

  return (
    <TouchableOpacity
      style={styles.likeUserItem}
      onPress={() => {
        if (userId === currentUser?.uid) {
          navigation.navigate('MainTabs', { screen: 'Profil' });
        } else {
          navigation.navigate('OtherProfile', { uid: userId });
        }
      }}
    >
      <Image
        source={
          userInfo.profilePic && avatarMap[userInfo.profilePic]
            ? avatarMap[userInfo.profilePic]
            : require('../assets/default-avatar.png')
        }
        style={styles.likeUserAvatar}
      />
      <Text style={styles.likeUserName}>
        {userInfo.username || 'Kullanıcı'}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 10 : 15,
    paddingBottom: 15,
    paddingHorizontal: 20,
    backgroundColor: '#000',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    minHeight: 60,
  },
  backButton: {
    marginRight: 15,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
    color: '#fff',
  },
  listContainer: {
    padding: 10,
    paddingBottom: 30,
  },
  card: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#0066CC'
  },
  userInfoColumn: {
    marginLeft: 10,
    marginTop: -5
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3498db',
    marginBottom: 2
  },
  userRank: {
    fontSize: 12,
    color: '#FFD700',
    marginLeft: 5,
    fontWeight: '600'
  },
  followButton: {
    backgroundColor: '#3498db',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 15,
    marginLeft: 8
  },
  followButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600'
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  postTypeLabel: {
    fontSize: 12,
    color: '#FFD700',
    fontWeight: 'bold',
    marginRight: 10
  },
  optionsButton: {
    padding: 4
  },
  cardContent: {
    marginVertical: 10
  },
  content: {
    fontSize: 15,
    color: '#fff',
    lineHeight: 22
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#333',
    paddingTop: 10,
    justifyContent: 'space-between'
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15
  },
  actionText: {
    color: '#fff',
    fontSize: 13,
    marginLeft: 5
  },
  timestampFooter: {
    fontSize: 12,
    color: '#aaa'
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContent: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    width: '90%',
    maxHeight: '70%'
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333'
  },
  modalTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold'
  },
  modalCloseButton: {
    padding: 4
  },
  modalList: {
    maxHeight: 300
  },
  likeUserItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333'
  },
  likeUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12
  },
  likeUserName: {
    color: '#fff',
    fontSize: 16
  },
  optionsModal: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 10,
    minWidth: 200
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8
  },
  optionText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 12
  }
});

export default SavedPostsScreen;
