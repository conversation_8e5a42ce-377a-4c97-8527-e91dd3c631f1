import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { auth, db } from '../firebase';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';

const HelpSupportScreen = ({ navigation }) => {
  const [category, setCategory] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const categories = [
    { id: 'bug', title: 'Hata Bildirimi', icon: 'bug-outline', color: '#FF6B6B' },
    { id: 'feature', title: 'Özellik İsteği', icon: 'lightbulb-outline', color: '#4ECDC4' },
    { id: 'account', title: 'Hesap <PERSON>runu', icon: 'person-circle-outline', color: '#45B7D1' },
    { id: 'content', title: '<PERSON><PERSON><PERSON><PERSON>runu', icon: 'document-text-outline', color: '#96CEB4' },
    { id: 'other', title: 'Diğer', icon: 'help-circle-outline', color: '#FFEAA7' },
  ];

  const handleSubmit = async () => {
    if (!category) {
      Alert.alert('Eksik Bilgi', 'Lütfen bir kategori seçin.');
      return;
    }
    
    if (!subject.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen konu başlığı yazın.');
      return;
    }
    
    if (!message.trim()) {
      Alert.alert('Eksik Bilgi', 'Lütfen mesajınızı yazın.');
      return;
    }

    setLoading(true);
    
    try {
      const user = auth.currentUser;
      const selectedCategory = categories.find(cat => cat.id === category);
      
      await addDoc(collection(db, 'helpRequests'), {
        userId: user.uid,
        userEmail: user.email,
        category: category,
        categoryTitle: selectedCategory.title,
        subject: subject.trim(),
        message: message.trim(),
        status: 'pending',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      Alert.alert(
        'Başarılı!',
        'Mesajınız başarıyla gönderildi. Destek ekibimiz en kısa sürede size dönüş yapacaktır.',
        [
          {
            text: 'Tamam',
            onPress: () => {
              setCategory('');
              setSubject('');
              setMessage('');
              navigation.goBack();
            }
          }
        ]
      );
    } catch (error) {
      console.error('Mesaj gönderme hatası:', error);
      Alert.alert('Hata', 'Mesaj gönderilirken bir sorun oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Yardım ve Destek</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.welcomeCard}>
          <Ionicons name="chatbubbles-outline" size={50} color="#45B7D1" />
          <Text style={styles.welcomeTitle}>Bize Ulaşın</Text>
          <Text style={styles.welcomeText}>
            Sorularınız, önerileriniz veya yaşadığınız sorunlar için 
            aşağıdaki formu doldurabilirsiniz. Size yardımcı olmaktan mutluluk duyarız.
          </Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Kategori</Text>
          <View style={styles.categoryGrid}>
            {categories.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.categoryCard,
                  category === item.id && [styles.selectedCard, { borderColor: item.color }]
                ]}
                onPress={() => setCategory(item.id)}
              >
                <Ionicons
                  name={item.icon}
                  size={28}
                  color={category === item.id ? item.color : '#666'}
                />
                <Text style={[
                  styles.categoryTitle,
                  category === item.id && { color: item.color }
                ]}>
                  {item.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <Text style={styles.sectionTitle}>Konu</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Konuyu kısaca özetleyin"
            placeholderTextColor="#888"
            value={subject}
            onChangeText={setSubject}
            maxLength={80}
          />

          <Text style={styles.sectionTitle}>Mesajınız</Text>
          <TextInput
            style={[styles.textInput, styles.messageInput]}
            placeholder="Sorununuzu veya önerinizi detaylı bir şekilde yazın..."
            placeholderTextColor="#888"
            value={message}
            onChangeText={setMessage}
            multiline
            numberOfLines={8}
            textAlignVertical="top"
            maxLength={500}
          />
          
          <Text style={styles.charCounter}>
            {message.length}/500 karakter
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.sendButton, loading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Ionicons name="paper-plane" size={20} color="#fff" />
              <Text style={styles.sendButtonText}>Mesajı Gönder</Text>
            </>
          )}
        </TouchableOpacity>

        <View style={styles.infoCard}>
          <Ionicons name="information-circle" size={24} color="#45B7D1" />
          <View style={styles.infoContent}>
            <Text style={styles.infoTitle}>Bilgilendirme</Text>
            <Text style={styles.infoText}> 
              • Acil durumlar için "Hesap Sorunu" kategorisini seçin{'\n'}
              • Tüm mesajlar güvenli bir şekilde saklanır
            </Text>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 15,
    backgroundColor: '#111',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  backButton: {
    padding: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSpacer: {
    width: 44,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  welcomeCard: {
    backgroundColor: '#111',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 25,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 12,
    marginBottom: 8,
  },
  welcomeText: {
    fontSize: 16,
    color: '#bbb',
    textAlign: 'center',
    lineHeight: 24,
  },
  formContainer: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 12,
    marginTop: 8,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  categoryCard: {
    width: '48%',
    backgroundColor: '#111',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedCard: {
    backgroundColor: 'rgba(69, 183, 209, 0.1)',
  },
  categoryTitle: {
    color: '#fff',
    fontSize: 13,
    fontWeight: '500',
    marginTop: 8,
    textAlign: 'center',
  },
  textInput: {
    backgroundColor: '#111',
    borderRadius: 12,
    padding: 16,
    color: '#fff',
    fontSize: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#333',
  },
  messageInput: {
    height: 140,
    textAlignVertical: 'top',
  },
  charCounter: {
    color: '#888',
    fontSize: 12,
    textAlign: 'right',
    marginTop: -12,
    marginBottom: 20,
  },
  sendButton: {
    backgroundColor: '#45B7D1',
    borderRadius: 12,
    padding: 18,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 25,
  },
  disabledButton: {
    backgroundColor: '#555',
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  infoCard: {
    backgroundColor: '#111',
    borderRadius: 12,
    padding: 18,
    flexDirection: 'row',
    marginBottom: 30,
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#45B7D1',
    marginBottom: 6,
  },
  infoText: {
    color: '#bbb',
    fontSize: 14,
    lineHeight: 20,
  },
});

export default HelpSupportScreen;
