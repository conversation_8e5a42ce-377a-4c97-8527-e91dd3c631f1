import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Image,
  TouchableOpacity,
  RefreshControl,
  Animated,
  Alert
} from 'react-native';
import {
  doc,
  getDoc,
  getDocs,
  updateDoc,
  arrayRemove,
  arrayUnion,
  collection,
  query,
  where,
  addDoc,
  deleteDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db, auth } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { updateUserPopularity, listenToUserPopularity } from '../utils/popularityUtils';
import { sendFollowRequest, getFollowRequest, cancelFollowRequest } from '../utils/followRequestUtils';
import { useToast } from '../contexts/ToastContext';
import { useFollowStatus } from '../contexts/FollowStatusContext';

// Avatar mapping: ProfileEditScreen’de "avatarX" şeklinde kaydediliyor.
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

const FollowListScreen = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [blockedUsers, setBlockedUsers] = useState([]);

  const route = useRoute();
  const navigation = useNavigation();
  const { showToast } = useToast();
  const {
    isFollowing: isFollowingGlobal,
    hasPendingRequest,
    handleFollowSuccess,
    handleUnfollowSuccess,
    handleFollowRequestSent,
    handleFollowRequestCancelled
  } = useFollowStatus();
  // route.params içerisinde;
  // type: 'followers' veya 'following'
  // userIds: [ 'uid1', 'uid2', ... ]
  // profileUid: profil sahibinin uid'si
  // isCurrentUserProfile: kendi profilimiz mi
  const { userIds, type, profileUid, isCurrentUserProfile } = route.params || {};
  const currentUid = auth.currentUser?.uid;

  // Gizli hesap kontrolü için state
  const [isPrivateAccount, setIsPrivateAccount] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [canViewList, setCanViewList] = useState(true);



  // Gizli hesap kontrolü
  useEffect(() => {
    const checkPrivacySettings = async () => {
      if (isCurrentUserProfile || !profileUid) {
        setCanViewList(true);
        return;
      }

      try {
        const userDoc = await getDoc(doc(db, 'users', profileUid));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const isPrivate = userData.isPrivateAccount || false;
          const isFollowingUser = userData.followers?.includes(currentUid) || false;

          setIsPrivateAccount(isPrivate);
          setIsFollowing(isFollowingUser);

          // Gizli hesapsa ve takip etmiyorsak listeyi gösterme
          if (isPrivate && !isFollowingUser) {
            setCanViewList(false);
          } else {
            setCanViewList(true);
          }
        }
      } catch (error) {
        console.error('Gizlilik ayarları kontrol hatası:', error);
        setCanViewList(true);
      }
    };

    checkPrivacySettings();
  }, [profileUid, isCurrentUserProfile, currentUid]);

  // Hatalı veya boş gelen userIds için
  useEffect(() => {
    if (!userIds || !Array.isArray(userIds)) {
      setUsers([]);
      setLoading(false);
      return;
    }

    if (canViewList) {
      fetchUsers();
    } else {
      setLoading(false);
    }
  }, [userIds, canViewList]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Önce mevcut kullanıcının engellenen listesini yükle
      const currentUserId = auth.currentUser?.uid;
      let currentUserBlockedUsers = [];

      if (currentUserId) {
        const currentUserDoc = await getDoc(doc(db, 'users', currentUserId));
        if (currentUserDoc.exists()) {
          const currentUserData = currentUserDoc.data();
          currentUserBlockedUsers = currentUserData.blockedUsers || [];
          setBlockedUsers(currentUserBlockedUsers);
        }
      }

      const userPromises = userIds.map(async (uid) => {
        const userDocRef = doc(db, 'users', uid);
        const snap = await getDoc(userDocRef);
        if (snap.exists()) {
          return { id: uid, ...snap.data() };
        }
        return null;
      });
      const results = (await Promise.all(userPromises)).filter(u => u !== null);

      // Engellenen kullanıcıları filtrele
      const filteredResults = results.filter(user => !currentUserBlockedUsers.includes(user.id));

      setUsers(filteredResults);
    } catch (error) {
      console.error('FollowListScreen error:', error);
    }
    setLoading(false);
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchUsers().then(() => setRefreshing(false));
  };

  const isFollowerList = type === 'followers';
  const isFollowingList = type === 'following';

  // Eğer takipçiler listesinden birini çıkarmak istiyorsak
  const handleRemoveFollower = async (uid) => {
    try {
      // Kullanıcı dokümanlarını güncelle
      await updateDoc(doc(db, 'users', currentUid), {
        followers: arrayRemove(uid),
      });
      await updateDoc(doc(db, 'users', uid), {
        following: arrayRemove(currentUid),
      });

      // follows koleksiyonundan da sil (ÖNEMLİ: Bu eksikti!)
      const q = query(
        collection(db, "follows"),
        where("followerUid", "==", uid),
        where("followingUid", "==", currentUid)
      );
      const snap = await getDocs(q);
      snap.forEach(async (doc) => {
        await deleteDoc(doc.ref);
      });

      // Optimistik: listeden ilgili kullanıcıyı çıkarın
      setUsers(prev => prev.filter(u => u.id !== uid));

      console.log(`Takipçi kaldırıldı: ${uid} artık ${currentUid} kullanıcısını takip etmiyor`);
    } catch (error) {
      console.error("Takipçi kaldırma hatası:", error);
    }
  };

  // Eğer takip edilenler listesinden birini çıkarmak istiyorsak
  const handleUnfollow = async (uid) => {
    try {
      // Kullanıcı dokümanlarını güncelle
      await updateDoc(doc(db, 'users', currentUid), {
        following: arrayRemove(uid),
      });
      await updateDoc(doc(db, 'users', uid), {
        followers: arrayRemove(currentUid),
      });

      // follows koleksiyonundan da sil (OtherProfileScreen ile uyumlu olması için)
      const q = query(
        collection(db, "follows"),
        where("followerUid", "==", currentUid),
        where("followingUid", "==", uid)
      );
      const snap = await getDocs(q);
      snap.forEach(async (doc) => {
        await deleteDoc(doc.ref);
      });

      // Kullanıcı listesini güncelle
      setUsers(prev => prev.map(u => {
        if (u.id === uid) {
          return {
            ...u,
            followers: (u.followers || []).filter(id => id !== currentUid)
          };
        }
        return u;
      }));

      // Eğer bu kullanıcı, şu anda görüntülenen profil sayfasındaki kullanıcıysa
      // ve bu liste başka bir kullanıcının takipçi/takip listesiyse
      const viewingProfileUid = route.params?.profileUid;
      if (viewingProfileUid && uid === viewingProfileUid) {
        // Profil sayfasına takip durumunu güncelleme sinyali gönder
        navigation.setParams({ followStatusChanged: true });
      }

      // Popülerlik değerini güncelle
      try {
        // Popülerlik değerini güncelle ve dinlemeye başla
        await updateUserPopularity(uid, true);

        // Kullanıcının popülerlik değişimini dinle
        listenToUserPopularity(uid, (updatedPopularity) => {
          // Kullanıcı listesini güncelle
          setUsers(prevUsers => {
            return prevUsers.map(user => {
              if (user.id === uid) {
                return {
                  ...user,
                  popularity: updatedPopularity
                };
              }
              return user;
            });
          });
        });
      } catch (popError) {
        console.error('Popülerlik güncelleme hatası:', popError);
      }

      // Global state'i güncelle
      handleUnfollowSuccess(uid);
    } catch (error) {
      console.error("Takipten bırakma hatası:", error);
    }
  };

  // Kullanıcıyı takip et
  const handleFollow = async (uid) => {
    // Engelleme kontrolü
    if (blockedUsers.includes(uid)) {
      showToast({
        message: 'Engellediğiniz kullanıcıyı takip edemezsiniz.',
        type: 'error'
      });
      return;
    }

    try {
      // Hedef kullanıcının gizli hesap olup olmadığını kontrol et
      const targetUserDoc = await getDoc(doc(db, 'users', uid));
      if (!targetUserDoc.exists()) return;

      const targetUserData = targetUserDoc.data();
      const isPrivateAccount = targetUserData.isPrivateAccount || false;

      // Hedef kullanıcının bizi engellemiş olup olmadığını kontrol et
      const targetUserBlockedUsers = targetUserData.blockedUsers || [];
      if (targetUserBlockedUsers.includes(currentUid)) {
        showToast({
          message: 'Bu kullanıcı sizi engellediği için takip edemezsiniz.',
          type: 'error'
        });
        return;
      }

      if (isPrivateAccount) {
        // Gizli hesap - takip isteği gönder
        const existingRequest = await getFollowRequest(currentUid, uid);
        if (existingRequest) {
          showToast({
            message: 'Bu kullanıcıya zaten takip isteği gönderilmiş.',
            type: 'info'
          });
          return;
        }

        const success = await sendFollowRequest(currentUid, uid);
        if (success) {
          handleFollowRequestSent(uid);
          showToast({
            message: 'İstek gönderildi',
            type: 'success'
          });
        } else {
          showToast({
            message: 'Takip isteği gönderilemedi.',
            type: 'error'
          });
        }
        return;
      }

      // Açık hesap - direkt takip et
      // Kullanıcı dokümanlarını güncelle
      await updateDoc(doc(db, 'users', currentUid), {
        following: arrayUnion(uid),
      });
      await updateDoc(doc(db, 'users', uid), {
        followers: arrayUnion(currentUid),
      });

      // follows koleksiyonuna ekle (OtherProfileScreen ile uyumlu olması için)
      await addDoc(collection(db, 'follows'), {
        followerUid: currentUid,
        followingUid: uid,
        createdAt: serverTimestamp()
      });

      // Kullanıcı listesini güncelle
      setUsers(prev => prev.map(u => {
        if (u.id === uid) {
          return {
            ...u,
            followers: [...(u.followers || []), currentUid]
          };
        }
        return u;
      }));

      // Eğer bu kullanıcı, şu anda görüntülenen profil sayfasındaki kullanıcıysa
      // ve bu liste başka bir kullanıcının takipçi/takip listesiyse
      const viewingProfileUid = route.params?.profileUid;
      if (viewingProfileUid && uid === viewingProfileUid) {
        // Profil sayfasına takip durumunu güncelleme sinyali gönder
        navigation.setParams({ followStatusChanged: true });
      }

      // Popülerlik değerini güncelle
      try {
        // Popülerlik değerini güncelle ve dinlemeye başla
        await updateUserPopularity(uid, true);

        // Kullanıcının popülerlik değişimini dinle
        listenToUserPopularity(uid, (updatedPopularity) => {
          // Kullanıcı listesini güncelle
          setUsers(prevUsers => {
            return prevUsers.map(user => {
              if (user.id === uid) {
                return {
                  ...user,
                  popularity: updatedPopularity
                };
              }
              return user;
            });
          });
        });
      } catch (popError) {
        console.error('Popülerlik güncelleme hatası:', popError);
      }

      // Global state'i güncelle
      handleFollowSuccess(uid);
    } catch (error) {
      console.error("Takip etme hatası:", error);
      showToast({
        message: 'Takip işlemi sırasında bir hata oluştu.',
        type: 'error'
      });
    }
  };

  // Takip isteğini iptal et
  const handleCancelFollowRequest = async (uid) => {
    if (!currentUid) return;

    try {
      const success = await cancelFollowRequest(currentUid, uid);
      if (success) {
        handleFollowRequestCancelled(uid);
        showToast({
          message: 'Takip isteği iptal edildi',
          type: 'success'
        });
      } else {
        showToast({
          message: 'İstek iptal edilirken hata oluştu',
          type: 'error'
        });
      }
    } catch (error) {
      console.error('Takip isteği iptal etme hatası:', error);
      showToast({
        message: 'Bir hata oluştu',
        type: 'error'
      });
    }
  };

  const renderItem = ({ item }) => {
    const isCurrentUserProfile = route.params?.isCurrentUserProfile !== false;
    const isFollowing = item.followers?.includes(currentUid);

    return (
      <View style={styles.userRow}>
        <TouchableOpacity
          style={{flexDirection: 'row', alignItems: 'center', flex: 1}}
          onPress={() => {
            if (item.id === currentUid) {
              navigation.navigate('MainTabs', { screen: 'Profil' });
            } else {
              navigation.navigate('OtherProfile', { uid: item.id });
            }
          }}
        >
          <Image
            source={
              item.profilePic && avatarMap[item.profilePic]
                ? avatarMap[item.profilePic]
                : require('../assets/default-avatar.png')
            }
            style={styles.avatar}
          />
          <Text style={styles.username}>{item.username || 'Kullanıcı'}</Text>
        </TouchableOpacity>

        {currentUid && item.id !== currentUid && (
          isCurrentUserProfile ? (
            <TouchableOpacity
              style={styles.followButton}
              onPress={() => {
                if (isFollowerList) {
                  handleRemoveFollower(item.id);
                } else if (isFollowingList) {
                  handleUnfollow(item.id);
                }
              }}
            >
              <Text style={styles.followButtonText}>
                {isFollowerList ? 'Çıkar' : isFollowingList ? 'Takibi Bırak' : 'Takip'}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.followButton, isFollowing ? styles.unfollowButton : {}]}
              onPress={() => {
                if (isFollowing) {
                  handleUnfollow(item.id);
                } else if (hasPendingRequest(item.id)) {
                  handleCancelFollowRequest(item.id);
                } else {
                  handleFollow(item.id);
                }
              }}
            >
              <Text style={styles.followButtonText}>
                {isFollowing
                  ? 'Takibi Bırak'
                  : hasPendingRequest(item.id)
                    ? 'İstek Gönderildi'
                    : 'Takip Et'
                }
              </Text>
            </TouchableOpacity>
          )
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Üst Bar */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isFollowerList ? 'Takipçiler' : 'Takip Edilenler'}
        </Text>
        <View style={styles.rightSpace} />
      </View>

      {!canViewList ? (
        <View style={styles.privateContainer}>
          <Ionicons name="lock-closed" size={64} color="#666" />
          <Text style={styles.privateText}>Bu hesap gizli</Text>
          <Text style={styles.privateSubtext}>
            {isFollowerList ? 'Takipçi' : 'Takip edilen'} listesini görmek için bu hesabı takip etmelisin
          </Text>
        </View>
      ) : users.length === 0 ? (
        <Text style={styles.noDataText}>
          Henüz {isFollowerList ? 'takipçiniz' : 'takip ettiğiniz'} yok.
        </Text>
      ) : (
        <FlatList
          data={users}
          keyExtractor={(item) => item.id.toString()}
          renderItem={renderItem}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

export default FollowListScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    backgroundColor: '#000',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: 20,
    color: '#fff',
    fontWeight: 'bold',
  },
  rightSpace: {
    width: 40,
  },
  noDataText: {
    color: '#aaa',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
  },
  listContainer: {
    paddingBottom: 20,
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomColor: '#333',
    borderBottomWidth: 1,
    marginHorizontal: 10,
  },
  avatar: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    borderWidth: 1,
    borderColor: '#0066CC',
    marginRight: 15,
  },
  username: {
    flex: 1,
    fontSize: 16,
    color: '#fff',
  },
  followButton: {
    backgroundColor: '#1DA1F2',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginLeft: 10,
  },
  followButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  unfollowButton: {
    backgroundColor: '#555',
  },
  loaderContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  privateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  privateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
  privateSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
});
