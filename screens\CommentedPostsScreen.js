import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
  Animated,
  Modal,
  Pressable,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  collection,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  serverTimestamp,
  addDoc,
  deleteDoc,
  query,
  where,
  onSnapshot
} from 'firebase/firestore';
import { auth, db } from '../firebase';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useToast } from '../contexts/ToastContext';
import { useNavigation } from '@react-navigation/native';
import { avatarMap } from '../utils/avatarMap';
import PostContent from '../components/PostContent';
import { createNotification, NOTIFICATION_TYPES } from '../utils/notificationUtils';
import { updateUserPopularity, listenToPostComments } from '../utils/popularityUtils';
import {
  sendFollowRequest,
  cancelFollowRequest,
  getFollowRequest
} from '../utils/followRequestUtils';

// Yardımcı: "x süre önce" formatı
function timeAgo(timestamp, currentTime) {
  if (!timestamp || !timestamp.toDate) return 'Bilinmeyen Tarih';
  const postTime = timestamp.toDate().getTime();
  const diff = Math.floor((currentTime - postTime) / 1000);
  if (diff < 0) return '0 saniye önce';
  if (diff < 60) return diff + ' saniye önce';
  if (diff < 3600) return Math.floor(diff / 60) + ' dakika önce';
  if (diff < 86400) return Math.floor(diff / 3600) + ' saat önce';
  return Math.floor(diff / 86400) + ' gün önce';
}

const CommentedPostsScreen = () => {
  const { theme } = useTheme();
  const { translations } = useLanguage();
  const { showToast } = useToast();
  const navigation = useNavigation();
  const [commentedPosts, setCommentedPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const currentUser = auth.currentUser;

  // FeedScreen'deki state'ler
  const [commentCounts, setCommentCounts] = useState({});
  const [followingIds, setFollowingIds] = useState([]);
  const [savedPosts, setSavedPosts] = useState([]);
  const [userData, setUserData] = useState(null);
  const [timeNow, setTimeNow] = useState(Date.now());
  const [blockedUsers, setBlockedUsers] = useState([]);

  // Beğeni animasyonu
  const likeAnimValues = useRef({});
  const [processingLikes, setProcessingLikes] = useState({});

  // Çift tıklama için
  const lastTapRef = useRef({});
  const doubleTapDelayMs = 300;

  // Modal state'leri
  const [optionsModalVisible, setOptionsModalVisible] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState(null);
  const [selectedPostOwnerId, setSelectedPostOwnerId] = useState(null);
  const [likesModalVisible, setLikesModalVisible] = useState(false);
  const [likesModalData, setLikesModalData] = useState([]);

  useEffect(() => {
    loadCommentedPosts();

    // Zaman güncelleyici
    const timeInterval = setInterval(() => {
      setTimeNow(Date.now());
    }, 60000);

    return () => clearInterval(timeInterval);
  }, []);

  // Kullanıcı verilerini dinle
  useEffect(() => {
    if (!currentUser) return;

    const userRef = doc(db, 'users', currentUser.uid);
    const unsubscribe = onSnapshot(userRef, (snap) => {
      if (snap.exists()) {
        const data = snap.data();
        setUserData(data);
        setFollowingIds(data.following || []);
        setBlockedUsers(data.blockedUsers || []);
      }
    });

    return () => unsubscribe();
  }, [currentUser]);

  // Kaydedilen gönderileri yükle
  useEffect(() => {
    if (!currentUser) return;

    const loadSavedPosts = async () => {
      try {
        const savedRef = collection(db, 'savedPosts');
        const q = query(savedRef, where('userId', '==', currentUser.uid));
        const snapshot = await getDocs(q);
        const savedPostIds = snapshot.docs.map(doc => doc.data().postId);
        setSavedPosts(savedPostIds);
      } catch (error) {
        console.error('Kaydedilen gönderiler yüklenirken hata:', error);
      }
    };

    loadSavedPosts();
  }, [currentUser]);

  const loadCommentedPosts = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      // Tüm gönderileri al
      const postsRef = collection(db, 'posts');
      const postsSnapshot = await getDocs(postsRef);
      
      // Önce yorum yapılan gönderileri filtrele
      let commentedPostsData = [];

      for (const postDoc of postsSnapshot.docs) {
        const postData = postDoc.data();

        // Bu gönderinin yorumlarını kontrol et
        try {
          // Güvenli kontroller ekle
          if (!postDoc.id || typeof postDoc.id !== 'string') {
            continue;
          }

          const commentsRef = collection(db, 'posts', postDoc.id, 'comments');
          const commentsSnapshot = await getDocs(commentsRef);

          // Kullanıcının bu gönderide yorumu var mı kontrol et
          let hasUserComment = false;
          let userComment = null;

          for (const commentDoc of commentsSnapshot.docs) {
            try {
              const commentData = commentDoc.data();

              // Güvenli kontroller
              if (!commentData || !commentData.uid || typeof commentData.uid !== 'string') {
                continue;
              }

              if (commentData.uid === currentUser.uid) {
                hasUserComment = true;
                userComment = {
                  id: commentDoc.id,
                  ...commentData
                };
                break;
              }
            } catch (commentError) {
              console.error('Yorum verisi işlenirken hata:', commentError);
              continue;
            }
          }

          if (hasUserComment) {
            commentedPostsData.push({
              id: postDoc.id,
              ...postData,
              likedBy: postData.likedBy || [],
              likes: postData.likes || 0,
              userComment: userComment,
            });
          }
        } catch (error) {
          console.error('Yorum kontrolü sırasında hata:', error);
          continue;
        }
      }

      // Kullanıcı ID'lerini topla (kendi kullanıcımızı da dahil et)
      const userIds = new Set();
      commentedPostsData.forEach(post => {
        if (post.uid) {
          userIds.add(post.uid);
        }
      });

      // Kullanıcı verilerini paralel olarak yükle
      const userCache = {};
      await Promise.all(Array.from(userIds).map(async (userId) => {
        try {
          const userDoc = await getDoc(doc(db, 'users', userId));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            userCache[userId] = userData;
            console.log(`Kullanıcı verisi yüklendi: ${userId}`, userData.username);
          } else {
            console.log(`Kullanıcı dokümanı bulunamadı: ${userId}`);
          }
        } catch (err) {
          console.error('Kullanıcı verisi yükleme hatası:', userId, err);
        }
      }));

      // Gönderi verilerini kullanıcı bilgileriyle birleştir (FeedScreen tarzı)
      commentedPostsData = commentedPostsData.filter(post => {
        if (post.uid && userCache[post.uid]) {
          const userData = userCache[post.uid];

          // Gönderi verilerini güncelle (FeedScreen'deki gibi Object.assign kullan)
          Object.assign(post, {
            profilePic: userData.profilePic || null,
            username: userData.username || 'Misafir',
            popularity: userData.popularity || 0,
            isPrivateAccount: userData.isPrivateAccount || false
          });
        } else {
          // Kullanıcı verisi bulunamazsa varsayılan değerler ata
          Object.assign(post, {
            profilePic: null,
            username: 'Misafir',
            popularity: 0,
            isPrivateAccount: false
          });
        }
        return true;
      });

      // Tarihe göre sırala (en yeni önce)
      commentedPostsData.sort((a, b) => {
        const aTime = a.createdAt?.toDate?.() || new Date(0);
        const bTime = b.createdAt?.toDate?.() || new Date(0);
        return bTime - aTime;
      });

      // Engellenen kullanıcıları filtrele
      const filteredPosts = commentedPostsData.filter(post => !blockedUsers.includes(post.uid));
      setCommentedPosts(filteredPosts);

      // Yorum sayılarını yükle
      const commentCountsData = {};
      for (const post of commentedPostsData) {
        try {
          const commentsRef = collection(db, 'posts', post.id, 'comments');
          const commentsSnapshot = await getDocs(commentsRef);
          commentCountsData[post.id] = commentsSnapshot.size;
        } catch (error) {
          console.error('Yorum sayısı yüklenirken hata:', error);
          commentCountsData[post.id] = 0;
        }
      }
      setCommentCounts(commentCountsData);

    } catch (error) {
      console.error('Yorum yapılan gönderiler yüklenirken hata:', error);
      showToast({
        message: 'Yorum yapılan gönderiler yüklenirken bir hata oluştu.',
        type: 'error'
      });
    }
    setLoading(false);
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadCommentedPosts().then(() => setRefreshing(false));
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      const now = new Date();
      const diffInSeconds = Math.floor((now - date) / 1000);
      
      if (diffInSeconds < 60) return 'Az önce';
      if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} dakika önce`;
      if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} saat önce`;
      if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} gün önce`;
      
      return date.toLocaleDateString('tr-TR');
    } catch (error) {
      return '';
    }
  };

  // Temel fonksiyonlar (LikedPostsScreen'den kopyalandı)
  const handleDoubleTap = (postId) => {
    if (processingLikes[postId]) return;
    if (!currentUser) return;

    const now = Date.now();
    const lastTap = lastTapRef.current[postId] || 0;
    lastTapRef.current[postId] = now;

    if (now - lastTap < doubleTapDelayMs) {
      const post = commentedPosts.find(p => p.id === postId);
      if (!post) return;
      toggleLike(postId);
    }
  };

  function toggleLike(postId) {
    if (!currentUser) return;
    if (processingLikes[postId]) return;

    setProcessingLikes(prev => ({ ...prev, [postId]: true }));

    if (!likeAnimValues.current[postId]) {
      likeAnimValues.current[postId] = new Animated.Value(1);
    }

    const post = commentedPosts.find(p => p.id === postId);
    if (!post) {
      setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      return;
    }

    const postRef = doc(db, 'posts', postId);

    getDoc(postRef).then(snap => {
      if (!snap.exists()) {
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        return;
      }

      const data = snap.data();
      const serverIsLiked = data.likedBy?.includes(currentUser.uid);
      const finalIsLiked = serverIsLiked;
      const newLikes = finalIsLiked ? Math.max(0, data.likes - 1) : (data.likes || 0) + 1;

      setCommentedPosts(prev => {
        return prev.map(p => {
          if (p.id === postId) {
            let updatedLikedBy;
            if (finalIsLiked) {
              updatedLikedBy = (p.likedBy || []).filter(uid => uid !== currentUser.uid);
            } else {
              updatedLikedBy = [...(p.likedBy || []), currentUser.uid];
            }
            return { ...p, likes: newLikes, likedBy: updatedLikedBy };
          }
          return p;
        });
      });

      Animated.sequence([
        Animated.timing(likeAnimValues.current[postId], {
          toValue: 1.2,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(likeAnimValues.current[postId], {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();

      updateDoc(postRef, {
        likes: newLikes,
        likedBy: finalIsLiked
          ? arrayRemove(currentUser.uid)
          : arrayUnion(currentUser.uid),
        lastLikeUpdate: serverTimestamp()
      }).finally(() => {
        setTimeout(() => {
          setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        }, 500);
      });
    }).catch(() => {
      setTimeout(() => {
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      }, 500);
    });
  }

  function handleCommentPress(postId) {
    navigation.navigate('Comments', { postId });
  }

  function openOptionsModal(postId, ownerId) {
    setSelectedPostId(postId);
    setSelectedPostOwnerId(ownerId);
    setOptionsModalVisible(true);
  }

  function handleSavePost(postId) {
    if (!currentUser || !postId) return;

    const isSaved = savedPosts.includes(postId);

    if (isSaved) {
      setSavedPosts(prev => prev.filter(id => id !== postId));
      showToast({ message: 'Gönderi kayıtlardan kaldırıldı.', type: 'success' });
    } else {
      setSavedPosts(prev => [...prev, postId]);
      showToast({ message: 'Gönderi kaydedildi.', type: 'success' });
    }

    const updateSaveStatus = async () => {
      try {
        if (isSaved) {
          const savedRef = collection(db, 'savedPosts');
          const q = query(savedRef, where('userId', '==', currentUser.uid), where('postId', '==', postId));
          getDocs(q).then(snapshot => {
            if (!snapshot.empty) {
              deleteDoc(snapshot.docs[0].ref).catch(err => console.error('Kayıt silme hatası:', err));
            }
          });
        } else {
          addDoc(collection(db, 'savedPosts'), {
            userId: currentUser.uid,
            postId: postId,
            savedAt: serverTimestamp()
          }).catch(err => console.error('Kaydetme hatası:', err));
        }
      } catch (error) {
        console.error('Kaydetme işlemi hatası:', error);
      }
    };

    updateSaveStatus();
  }

  async function handleToggleFollow(targetUid) {
    if (!currentUser || targetUid === currentUser.uid) return;

    // Engelleme kontrolü
    if (blockedUsers.includes(targetUid)) {
      showToast({
        message: 'Engellediğiniz kullanıcıyı takip edemezsiniz.',
        type: 'error'
      });
      return;
    }

    try {
      const isFollowing = followingIds.includes(targetUid);

      if (isFollowing) {
        const userRef = doc(db, 'users', currentUser.uid);
        await updateDoc(userRef, { following: arrayRemove(targetUid) });
        const targetRef = doc(db, 'users', targetUid);
        await updateDoc(targetRef, { followers: arrayRemove(currentUser.uid) });
        setFollowingIds(prev => prev.filter(id => id !== targetUid));
        showToast({ message: 'Takipten çıkıldı.', type: 'success' });
      } else {
        // Hedef kullanıcının gizli hesap olup olmadığını kontrol et
        const targetUserDoc = await getDoc(doc(db, 'users', targetUid));
        if (!targetUserDoc.exists()) return;

        const targetUserData = targetUserDoc.data();
        const isPrivateAccount = targetUserData.isPrivateAccount || false;

        // Hedef kullanıcının bizi engellemiş olup olmadığını kontrol et
        const targetUserBlockedUsers = targetUserData.blockedUsers || [];
        if (targetUserBlockedUsers.includes(currentUser.uid)) {
          showToast({
            message: 'Bu kullanıcı sizi engellediği için takip edemezsiniz.',
            type: 'error'
          });
          return;
        }

        if (isPrivateAccount) {
          // Gizli hesap - takip isteği gönder
          const existingRequest = await getFollowRequest(currentUser.uid, targetUid);
          if (existingRequest) {
            showToast({
              message: 'Bu kullanıcıya zaten takip isteği gönderilmiş.',
              type: 'info'
            });
            return;
          }

          const success = await sendFollowRequest(currentUser.uid, targetUid);
          if (success) {
            showToast({
              message: 'Takip isteği gönderildi. Kullanıcı isteğinizi kabul ettiğinde takip edeceksiniz.',
              type: 'success'
            });
          } else {
            showToast({
              message: 'Takip isteği gönderilemedi.',
              type: 'error'
            });
          }
        } else {
          // Açık hesap - direkt takip et
          const userRef = doc(db, 'users', currentUser.uid);
          await updateDoc(userRef, { following: arrayUnion(targetUid) });
          const targetRef = doc(db, 'users', targetUid);
          await updateDoc(targetRef, { followers: arrayUnion(currentUser.uid) });
          setFollowingIds(prev => [...prev, targetUid]);
          showToast({ message: 'Takip edildi.', type: 'success' });
        }
      }
    } catch (error) {
      console.error('Takip işlemi hatası:', error);
      showToast({ message: 'Bir hata oluştu', type: 'error' });
    }
  }

  function handleShowLikes(likedBy) {
    if (!likedBy || likedBy.length === 0) return;
    setLikesModalData(likedBy);
    setLikesModalVisible(true);
  }

  // Gönderi bildirme
  async function handleReportPost() {
    if (!currentUser || !selectedPostId) return;

    try {
      await addDoc(collection(db, 'reports'), {
        postId: selectedPostId,
        reportedBy: currentUser.uid,
        reason: 'inappropriate',
        timestamp: serverTimestamp(),
        status: 'pending'
      });

      showToast({
        message: 'Gönderi bildirildi. İnceleme sonrası gerekli işlemler yapılacaktır.',
        type: 'success'
      });
    } catch (error) {
      console.error('Bildirim hatası:', error);
      showToast({
        message: 'Bildirim gönderilirken bir hata oluştu.',
        type: 'error'
      });
    }
  }

  // Kullanıcı engelleme
  async function handleBlockUser(targetUid) {
    if (!currentUser || !targetUid || targetUid === currentUser.uid) return;

    try {
      const userRef = doc(db, 'users', currentUser.uid);
      const targetUserRef = doc(db, 'users', targetUid);

      // ÇİFT YÖNLÜ ENGELLEME - Her iki kullanıcı da birbirini engellesin
      await updateDoc(userRef, {
        blockedUsers: arrayUnion(targetUid)
      });
      await updateDoc(targetUserRef, {
        blockedUsers: arrayUnion(currentUser.uid)
      });

      // Takip ilişkilerini kaldır
      // 1. Eğer ben onu takip ediyorsam, takipten çık
      if (followingIds.includes(targetUid)) {
        await updateDoc(userRef, {
          following: arrayRemove(targetUid)
        });
        await updateDoc(targetUserRef, {
          followers: arrayRemove(currentUser.uid)
        });

        // follows koleksiyonundan da sil
        const followQuery = query(
          collection(db, 'follows'),
          where('followerUid', '==', currentUser.uid),
          where('followingUid', '==', targetUid)
        );
        const followDocs = await getDocs(followQuery);
        followDocs.forEach(async (doc) => {
          await deleteDoc(doc.ref);
        });

        // UI'ı güncelle
        setFollowingIds(prev => prev.filter(id => id !== targetUid));
      }

      // 2. Eğer o beni takip ediyorsa, onu takipçilerimden çıkar
      const userDoc = await getDoc(userRef);
      if (userDoc.exists()) {
        const userData = userDoc.data();
        const myFollowers = userData.followers || [];

        if (myFollowers.includes(targetUid)) {
          await updateDoc(userRef, {
            followers: arrayRemove(targetUid)
          });
          await updateDoc(targetUserRef, {
            following: arrayRemove(currentUser.uid)
          });

          // follows koleksiyonundan da sil
          const reverseFollowQuery = query(
            collection(db, 'follows'),
            where('followerUid', '==', targetUid),
            where('followingUid', '==', currentUser.uid)
          );
          const reverseFollowDocs = await getDocs(reverseFollowQuery);
          reverseFollowDocs.forEach(async (doc) => {
            await deleteDoc(doc.ref);
          });
        }
      }

      // Engellenen kullanıcının gönderilerini listeden kaldır
      setCommentedPosts(prev => prev.filter(post => post.uid !== targetUid));

      showToast({
        message: 'Kullanıcı engellendi.',
        type: 'success'
      });
    } catch (error) {
      console.error('Engelleme hatası:', error);
      showToast({
        message: 'Engelleme işlemi sırasında bir hata oluştu.',
        type: 'error'
      });
    }
  }

  const renderPostItem = ({ item }) => {
    const displayName = (item.username && item.username.trim() !== '') ? item.username : 'Misafir';
    const isLiked = (item.likedBy || []).includes(currentUser?.uid ?? '');
    const commentCount = commentCounts[item.id] || 0;
    const isFollowing = followingIds.includes(item.uid);
    const isMyPost = item.uid === currentUser?.uid;

    if (!likeAnimValues.current[item.id]) {
      likeAnimValues.current[item.id] = new Animated.Value(1);
    }

    const userRank =
      item.popularity >= 1000 ? 'Efsane' :
      item.popularity >= 500 ? 'Yıldız' :
      item.popularity >= 200 ? 'Popüler' :
      item.popularity >= 100 ? 'Yükselen' :
      item.popularity >= 50 ? 'Aktif' :
      item.popularity >= 20 ? 'Başlangıç' :
      item.popularity > 0 ? 'Yeni Üye' : '';

    return (
      <View style={styles.card}>
        <View style={styles.postHeader}>
          <View style={styles.headerLeft}>
            <TouchableOpacity
              onPress={() => {
                requestAnimationFrame(() => {
                  if (item.uid === currentUser?.uid) {
                    navigation.navigate('MainTabs', { screen: 'Profil' });
                  } else {
                    navigation.navigate('OtherProfile', { uid: item.uid });
                  }
                });
              }}
              activeOpacity={0.7}
            >
              <Image
                source={
                  isMyPost && userData && userData.avatarIndex !== undefined
                    ? avatarMap['avatar' + (userData.avatarIndex + 1)]
                    : item.profilePic && avatarMap[item.profilePic]
                      ? avatarMap[item.profilePic]
                      : require('../assets/default-avatar.png')
                }
                style={styles.avatar}
              />
            </TouchableOpacity>
            <View style={styles.userInfoColumn}>
              <View style={styles.userRow}>
                <TouchableOpacity
                  onPress={() => {
                    requestAnimationFrame(() => {
                      if (item.uid === currentUser?.uid) {
                        navigation.navigate('MainTabs', { screen: 'Profil' });
                      } else {
                        navigation.navigate('OtherProfile', { uid: item.uid });
                      }
                    });
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.username}>{displayName}</Text>
                  <Text style={styles.userRank}>{userRank || 'Yeni Üye'}</Text>
                </TouchableOpacity>
                {!isMyPost && (
                  <TouchableOpacity
                    style={[styles.followButton, { marginLeft: 8 }]}
                    onPress={() => handleToggleFollow(item.uid)}
                  >
                    <Text style={styles.followButtonText}>
                      {isFollowing ? 'Takipten Çık' : 'Takip Et'}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>
          <View style={styles.headerRight}>
            <Text style={styles.postTypeLabel}>
              {item.type === 'Şarkı Sözü' ? 'Şarkı' : item.type === 'Şiir' ? 'Şiir' : item.type}
            </Text>
            <TouchableOpacity
              style={styles.optionsButton}
              onPress={() => openOptionsModal(item.id, item.uid)}
            >
              <Ionicons name="ellipsis-horizontal" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>

        <TouchableOpacity
          style={styles.cardContent}
          activeOpacity={0.9}
          onPress={() => handleDoubleTap(item.id)}
        >
          <PostContent content={item.content} maxLength={150} style={styles.content} />
        </TouchableOpacity>

        <View style={styles.cardFooter}>
          <View style={styles.footerLeft}>
            <TouchableOpacity
              style={styles.likeContainer}
              onPress={() => toggleLike(item.id)}
              onLongPress={() => handleShowLikes(item.likedBy)}
              disabled={processingLikes[item.id]}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Animated.View style={{ transform: [{ scale: likeAnimValues.current[item.id] }] }}>
                <Ionicons
                  name={isLiked ? 'heart' : 'heart-outline'}
                  size={24}
                  color={isLiked ? '#e74c3c' : '#fff'}
                />
              </Animated.View>
              <Text style={[styles.actionText, { marginLeft: 6 }]}>{item.likes}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { marginLeft: -2 }]}
              onPress={() => handleCommentPress(item.id)}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Ionicons name="chatbubble-outline" size={24} color="#fff" />
              <Text style={styles.actionText}>Yorum ({commentCount})</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { marginLeft: 10 }]}
              onPress={() => handleSavePost(item.id)}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Ionicons
                name={savedPosts.includes(item.id) ? "bookmark" : "bookmark-outline"}
                size={22}
                color="#fff"
              />
              <Text style={styles.actionText}>{savedPosts.includes(item.id) ? "Kaydedildi" : "Kaydet"}</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.timestampFooter}>
            {timeAgo(item.createdAt, timeNow)}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={[styles.header, { backgroundColor: theme.primary }]}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Yorum Yapılan Gönderiler</Text>
      </View>

      {loading ? (
        <ActivityIndicator size="large" color={theme.primary} style={styles.loader} />
      ) : commentedPosts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="chatbubble-outline" size={64} color={theme.text} />
          <Text style={[styles.emptyText, { color: theme.text }]}>
            Henüz yorum yapılan gönderi bulunmamaktadır
          </Text>
        </View>
      ) : (
        <FlatList
          data={commentedPosts}
          renderItem={renderPostItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={theme.text}
            />
          }
          contentContainerStyle={styles.listContainer}
        />
      )}

      {/* Beğeniler Modal */}
      {likesModalVisible && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={likesModalVisible}
          onRequestClose={() => setLikesModalVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Beğenenler</Text>
                <TouchableOpacity
                  onPress={() => setLikesModalVisible(false)}
                  style={styles.modalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              <FlatList
                data={likesModalData}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item: userId }) => (
                  <LikeUserItem userId={userId} />
                )}
                style={styles.modalList}
              />
            </View>
          </View>
        </Modal>
      )}

      {/* Seçenekler Modal */}
      {optionsModalVisible && (
        <Modal
          animationType="fade"
          transparent={true}
          visible={optionsModalVisible}
          onRequestClose={() => setOptionsModalVisible(false)}
        >
          <Pressable
            style={styles.modalOverlay}
            onPress={() => setOptionsModalVisible(false)}
          >
            <View style={styles.optionsModalContent}>
              <TouchableOpacity
                style={styles.optionItem}
                onPress={() => {
                  setOptionsModalVisible(false);
                  handleReportPost();
                }}
              >
                <Ionicons name="flag" size={20} color="#ff4444" />
                <Text style={styles.optionText}>Bildir</Text>
              </TouchableOpacity>

              {selectedPostOwnerId !== currentUser?.uid && (
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={() => {
                    setOptionsModalVisible(false);
                    handleBlockUser(selectedPostOwnerId);
                  }}
                >
                  <Ionicons name="ban" size={20} color="#ff4444" />
                  <Text style={styles.optionText}>Engelle</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={styles.optionItem}
                onPress={() => setOptionsModalVisible(false)}
              >
                <Ionicons name="close" size={20} color="#888" />
                <Text style={[styles.optionText, { color: '#888' }]}>İptal</Text>
              </TouchableOpacity>
            </View>
          </Pressable>
        </Modal>
      )}
    </View>
  );
};

// Beğeni modal'ındaki kullanıcı item'ı
const LikeUserItem = ({ userId }) => {
  const [userInfo, setUserInfo] = useState(null);
  const navigation = useNavigation();
  const currentUser = auth.currentUser;

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          setUserInfo(userSnap.data());
        }
      } catch (error) {
        console.error('Kullanıcı bilgisi alınırken hata:', error);
      }
    };

    fetchUserInfo();
  }, [userId]);

  if (!userInfo) return null;

  return (
    <TouchableOpacity
      style={styles.likeUserItem}
      onPress={() => {
        if (userId === currentUser?.uid) {
          navigation.navigate('MainTabs', { screen: 'Profil' });
        } else {
          navigation.navigate('OtherProfile', { uid: userId });
        }
      }}
    >
      <Image
        source={
          userInfo.profilePic && avatarMap[userInfo.profilePic]
            ? avatarMap[userInfo.profilePic]
            : require('../assets/default-avatar.png')
        }
        style={styles.likeUserAvatar}
      />
      <Text style={styles.likeUserName}>
        {userInfo.username || 'Kullanıcı'}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingBottom: 15,
    paddingHorizontal: 20,
    backgroundColor: '#000',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  backButton: {
    marginRight: 15,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
    color: '#fff',
  },
  listContainer: {
    padding: 10,
    paddingBottom: 30,
  },
  card: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#0066CC'
  },
  userInfoColumn: {
    marginLeft: 10,
    marginTop: -5
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3498db',
    marginBottom: 2
  },
  userRank: {
    fontSize: 12,
    color: '#FFD700',
    marginLeft: 5,
    fontWeight: '600'
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  postTypeLabel: {
    fontSize: 12,
    color: '#FFD700',
    fontWeight: 'bold',
    marginRight: 10
  },
  commentedIndicator: {
    padding: 5
  },
  cardContent: {
    marginVertical: 10
  },
  content: {
    fontSize: 15,
    color: '#fff',
    lineHeight: 22
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#333',
    paddingTop: 10,
    justifyContent: 'space-between'
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  actionText: {
    fontSize: 14,
    color: '#fff',
    marginLeft: 6
  },
  timestampFooter: {
    fontSize: 12,
    color: '#888'
  },
  followButton: {
    backgroundColor: '#0066CC',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8
  },
  followButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600'
  },
  optionsButton: {
    padding: 5
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxHeight: '60%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalList: {
    maxHeight: 300,
  },
  likeUserItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 5,
  },
  likeUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  likeUserName: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
  optionsModalContent: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 10,
    minWidth: 200,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 15,
  },
  optionText: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 12,
    fontWeight: '500',
  },
});

export default CommentedPostsScreen;
