import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  Alert,
  Modal,
  TextInput,
  Switch,
  ToastAndroid,
  TouchableWithoutFeedback,
  Keyboard
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { auth, db } from '../firebase';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  doc, 
  getDoc,
  orderBy,
  limit,
  startAfter
} from 'firebase/firestore';
import { useFollowStatus } from '../contexts/FollowStatusContext';
import { useToast } from '../contexts/ToastContext';
import { avatarMap } from '../utils/avatarMap';
import { 
  sendFollowRequest, 
  cancelFollowRequest,
  getFollowRequest 
} from '../utils/followRequestUtils';

const BulkFollowManagementScreen = () => {
  const navigation = useNavigation();
  const { showToast } = useToast();
  const {
    isFollowing,
    hasPendingRequest,
    handleFollowSuccess,
    handleUnfollowSuccess,
    handleFollowRequestSent,
    handleFollowRequestCancelled
  } = useFollowStatus();

  // State
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    showFollowing: true,
    showNotFollowing: true,
    sortBy: 'newest' // newest, popularity_asc, popularity_desc
  });
  const [processingBulkAction, setProcessingBulkAction] = useState(false);
  const [lastDoc, setLastDoc] = useState(null);
  const [hasMore, setHasMore] = useState(true);

  const currentUser = auth.currentUser;

  useEffect(() => {
    loadUsers();
  }, [filters]);

  const loadUsers = async (loadMore = false) => {
    if (!currentUser) return;

    try {
      if (!loadMore) {
        setLoading(true);
        setUsers([]);
        setLastDoc(null);
        setHasMore(true);
      }

      // Bize gelen takip isteklerini getir
      const followRequestsQuery = query(
        collection(db, 'followRequests'),
        where('toUserId', '==', currentUser.uid),
        where('status', '==', 'pending')
      );

      const requestsSnapshot = await getDocs(followRequestsQuery);

      if (requestsSnapshot.empty) {
        setHasMore(false);
        setUsers([]);
        return;
      }

      // İstek gönderen kullanıcıların ID'lerini al
      const requesterIds = [];
      const requestsData = {};
      requestsSnapshot.forEach(doc => {
        const requestData = doc.data();
        requesterIds.push(requestData.fromUserId);
        requestsData[requestData.fromUserId] = {
          requestId: doc.id,
          ...requestData
        };
      });

      // Bu kullanıcıların bilgilerini getir
      const newUsers = [];
      for (const userId of requesterIds) {
        try {
          const userDoc = await getDoc(doc(db, 'users', userId));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            newUsers.push({
              id: userDoc.id,
              requestId: requestsData[userId].requestId,
              ...userData
            });
          }
        } catch (error) {
          console.error('Kullanıcı bilgisi alınamadı:', error);
        }
      }

      // Client-side filtreleme ve sıralama
      const filteredUsers = filterAndSortUsers(newUsers);

      setUsers(filteredUsers);
      setHasMore(false); // Takip istekleri için sayfalama yok

    } catch (error) {
      console.error('Kullanıcılar yüklenirken hata:', error);
      showToast({
        message: 'Kullanıcılar yüklenirken hata oluştu',
        type: 'error'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterAndSortUsers = (userList) => {
    // Önce filtreleme
    let filtered = userList.filter(user => {
      // Arama filtresi
      if (searchQuery && !user.username?.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Takip durumu filtreleri
      const isFollowingUser = isFollowing(user.id);

      if (!filters.showFollowing && isFollowingUser) return false;
      if (!filters.showNotFollowing && !isFollowingUser) return false;

      return true;
    });

    // Sonra sıralama
    filtered.sort((a, b) => {
      if (filters.sortBy === 'newest') {
        const aDate = a.createdAt?.toDate?.() || new Date(0);
        const bDate = b.createdAt?.toDate?.() || new Date(0);
        return bDate - aDate; // En yeni önce
      } else if (filters.sortBy === 'popularity_asc') {
        const aPopularity = a.popularity || 0;
        const bPopularity = b.popularity || 0;
        return aPopularity - bPopularity; // Artan
      } else if (filters.sortBy === 'popularity_desc') {
        const aPopularity = a.popularity || 0;
        const bPopularity = b.popularity || 0;
        return bPopularity - aPopularity; // Azalan
      }
      return 0;
    });

    return filtered;
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setSelectedUsers(new Set());
    setSelectAll(false);
    loadUsers();
  }, [filters]);

  const loadMoreUsers = () => {
    if (!loading && hasMore) {
      loadUsers(true);
    }
  };

  const toggleUserSelection = (userId) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
    setSelectAll(newSelected.size === users.length);
  };

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedUsers(new Set());
    } else {
      setSelectedUsers(new Set(users.map(user => user.id)));
    }
    setSelectAll(!selectAll);
  };

  const handleBulkFollow = async () => {
    if (selectedUsers.size === 0) {
      showToast({
        message: 'Lütfen en az bir kullanıcı seçin',
        type: 'warning'
      });
      return;
    }

    Alert.alert(
      'Toplu Takip',
      `${selectedUsers.size} kullanıcıya takip isteği gönderilsin mi?`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Gönder', onPress: performBulkFollow }
      ]
    );
  };

  const performBulkFollow = async () => {
    setProcessingBulkAction(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const userId of selectedUsers) {
        try {
          const user = users.find(u => u.id === userId);
          if (!user) continue;

          if (isFollowing(userId)) {
            continue; // Zaten takip ediliyor
          }

          if (hasPendingRequest(userId)) {
            continue; // Zaten istek gönderilmiş
          }

          const success = await sendFollowRequest(currentUser.uid, userId);
          if (success) {
            handleFollowRequestSent(userId);
            successCount++;
          } else {
            errorCount++;
          }

          // Rate limiting için kısa bekleme
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.error(`Kullanıcı ${userId} için takip isteği hatası:`, error);
          errorCount++;
        }
      }

      showToast({
        message: `${successCount} istek gönderildi${errorCount > 0 ? `, ${errorCount} hata` : ''}`,
        type: successCount > 0 ? 'success' : 'error'
      });

      setSelectedUsers(new Set());
      setSelectAll(false);
    } catch (error) {
      console.error('Toplu takip hatası:', error);
      showToast({
        message: 'Toplu takip işlemi sırasında hata oluştu',
        type: 'error'
      });
    } finally {
      setProcessingBulkAction(false);
    }
  };

  const handleBulkUnfollow = async () => {
    if (selectedUsers.size === 0) {
      showToast({
        message: 'Lütfen en az bir kullanıcı seçin',
        type: 'warning'
      });
      return;
    }

    const followingUsers = Array.from(selectedUsers).filter(userId => isFollowing(userId));
    const pendingUsers = Array.from(selectedUsers).filter(userId => hasPendingRequest(userId));

    if (followingUsers.length === 0 && pendingUsers.length === 0) {
      showToast({
        message: 'Seçilen kullanıcılar arasında takip edilen veya bekleyen istek yok',
        type: 'warning'
      });
      return;
    }

    Alert.alert(
      'Toplu İptal',
      `${followingUsers.length} takip + ${pendingUsers.length} bekleyen istek iptal edilsin mi?`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'İptal Et', onPress: performBulkUnfollow, style: 'destructive' }
      ]
    );
  };

  const performBulkUnfollow = async () => {
    setProcessingBulkAction(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const userId of selectedUsers) {
        try {
          if (hasPendingRequest(userId)) {
            const success = await cancelFollowRequest(currentUser.uid, userId);
            if (success) {
              handleFollowRequestCancelled(userId);
              successCount++;
            } else {
              errorCount++;
            }
          } else if (isFollowing(userId)) {
            // Takipten çıkma işlemi burada yapılacak
            // Bu işlem için ayrı bir fonksiyon gerekebilir
            successCount++;
          }

          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          console.error(`Kullanıcı ${userId} için iptal hatası:`, error);
          errorCount++;
        }
      }

      showToast({
        message: `${successCount} işlem tamamlandı${errorCount > 0 ? `, ${errorCount} hata` : ''}`,
        type: successCount > 0 ? 'success' : 'error'
      });

      setSelectedUsers(new Set());
      setSelectAll(false);
    } catch (error) {
      console.error('Toplu iptal hatası:', error);
      showToast({
        message: 'Toplu iptal işlemi sırasında hata oluştu',
        type: 'error'
      });
    } finally {
      setProcessingBulkAction(false);
    }
  };

  const renderUserItem = ({ item }) => {
    const isSelected = selectedUsers.has(item.id);
    const isFollowingUser = isFollowing(item.id);
    const hasPending = hasPendingRequest(item.id);
    const isPrivate = item.isPrivateAccount || false;

    const getStatusIcon = () => {
      if (isFollowingUser) {
        return <Ionicons name="checkmark-circle" size={20} color="#2ecc71" />;
      } else if (hasPending) {
        return <Ionicons name="time-outline" size={20} color="#f39c12" />;
      } else if (isPrivate) {
        return <Ionicons name="lock-closed" size={16} color="#95a5a6" />;
      } else {
        return <Ionicons name="person-add-outline" size={20} color="#3498db" />;
      }
    };

    const getStatusText = () => {
      if (isFollowingUser) return 'Takip Ediliyor';
      if (hasPending) return 'İstek Gönderildi';
      if (isPrivate) return 'Gizli Hesap';
      return 'Takip Et';
    };

    return (
      <TouchableOpacity
        style={[styles.userItem, isSelected && styles.selectedUserItem]}
        onPress={() => toggleUserSelection(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.userItemLeft}>
          <TouchableOpacity
            style={[styles.checkbox, isSelected && styles.checkedBox]}
            onPress={() => toggleUserSelection(item.id)}
          >
            {isSelected && <Ionicons name="checkmark" size={16} color="#fff" />}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => navigation.navigate('OtherProfile', { uid: item.id })}
          >
            <Image
              source={
                item.profilePic && avatarMap[item.profilePic]
                  ? avatarMap[item.profilePic]
                  : require('../assets/default-avatar.png')
              }
              style={styles.userAvatar}
            />
          </TouchableOpacity>

          <View style={styles.userInfo}>
            <TouchableOpacity
              onPress={() => navigation.navigate('OtherProfile', { uid: item.id })}
            >
              <Text style={styles.username}>{item.username || 'Kullanıcı'}</Text>
              {item.fullName && (
                <Text style={styles.fullName}>{item.fullName}</Text>
              )}
            </TouchableOpacity>

            <View style={styles.statusContainer}>
              {getStatusIcon()}
              <Text style={[styles.statusText,
                isFollowingUser && styles.followingText,
                hasPending && styles.pendingText
              ]}>
                {getStatusText()}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.userItemRight}>
          {item.popularity && (
            <View style={styles.popularityBadge}>
              <Ionicons name="star" size={12} color="#FFD700" />
              <Text style={styles.popularityText}>{item.popularity}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderFilterModal = () => (
    <Modal
      visible={filterModalVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setFilterModalVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filtreler</Text>
            <TouchableOpacity
              onPress={() => setFilterModalVisible(false)}
              style={styles.modalCloseButton}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Takip Durumu</Text>

            <View style={styles.filterOption}>
              <Text style={styles.filterOptionText}>Takip Edilenler</Text>
              <Switch
                value={filters.showFollowing}
                onValueChange={(value) => setFilters(prev => ({ ...prev, showFollowing: value }))}
                trackColor={{ false: '#333', true: '#0066CC' }}
                thumbColor={filters.showFollowing ? '#fff' : '#666'}
              />
            </View>

            <View style={styles.filterOption}>
              <Text style={styles.filterOptionText}>Takip Edilmeyenler</Text>
              <Switch
                value={filters.showNotFollowing}
                onValueChange={(value) => setFilters(prev => ({ ...prev, showNotFollowing: value }))}
                trackColor={{ false: '#333', true: '#0066CC' }}
                thumbColor={filters.showNotFollowing ? '#fff' : '#666'}
              />
            </View>

          </View>

          <View style={styles.filterSection}>
            <Text style={styles.filterSectionTitle}>Sıralama</Text>

            <TouchableOpacity
              style={[styles.sortOption, filters.sortBy === 'newest' && styles.selectedSortOption]}
              onPress={() => setFilters(prev => ({ ...prev, sortBy: 'newest' }))}
            >
              <Ionicons name="time-outline" size={20} color={filters.sortBy === 'newest' ? '#0066CC' : '#fff'} />
              <Text style={[styles.sortOptionText, filters.sortBy === 'newest' && styles.selectedSortText]}>
                En Yeni
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.sortOption, filters.sortBy === 'popularity_asc' && styles.selectedSortOption]}
              onPress={() => setFilters(prev => ({ ...prev, sortBy: 'popularity_asc' }))}
            >
              <Ionicons name="trending-up-outline" size={20} color={filters.sortBy === 'popularity_asc' ? '#0066CC' : '#fff'} />
              <Text style={[styles.sortOptionText, filters.sortBy === 'popularity_asc' && styles.selectedSortText]}>
                Popülerlik Artan
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.sortOption, filters.sortBy === 'popularity_desc' && styles.selectedSortOption]}
              onPress={() => setFilters(prev => ({ ...prev, sortBy: 'popularity_desc' }))}
            >
              <Ionicons name="trending-down-outline" size={20} color={filters.sortBy === 'popularity_desc' ? '#0066CC' : '#fff'} />
              <Text style={[styles.sortOptionText, filters.sortBy === 'popularity_desc' && styles.selectedSortText]}>
                Popülerlik Azalan
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.applyFiltersButton}
            onPress={() => {
              setFilterModalVisible(false);
              loadUsers();
            }}
          >
            <Text style={styles.applyFiltersButtonText}>Filtreleri Uygula</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  if (loading && users.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Toplu Takip Yönetimi</Text>
          <View style={styles.rightSpace} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0066CC" />
          <Text style={styles.loadingText}>Kullanıcılar yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Toplu Takip Yönetimi</Text>
        <TouchableOpacity
          onPress={() => setFilterModalVisible(true)}
          style={styles.filterButton}
        >
          <Ionicons name="filter" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Kullanıcı ara..."
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Selection Controls */}
      <View style={styles.selectionControls}>
        <TouchableOpacity
          style={styles.selectAllButton}
          onPress={toggleSelectAll}
        >
          <Ionicons
            name={selectAll ? "checkbox" : "square-outline"}
            size={20}
            color="#0066CC"
          />
          <Text style={styles.selectAllText}>
            {selectAll ? 'Tümünü Kaldır' : 'Tümünü Seç'}
          </Text>
        </TouchableOpacity>

        <Text style={styles.selectedCount}>
          {selectedUsers.size} / {users.length} seçili
        </Text>
      </View>

      {/* User List */}
      {users.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="people-outline" size={64} color="#666" />
          <Text style={styles.emptyText}>
            {searchQuery ? 'Arama sonucu bulunamadı' : 'Kullanıcı bulunamadı'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={users}
          renderItem={renderUserItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#0066CC"
            />
          }
          onEndReached={loadMoreUsers}
          onEndReachedThreshold={0.1}
          ListFooterComponent={() => (
            hasMore && users.length > 0 ? (
              <View style={styles.loadMoreContainer}>
                <ActivityIndicator size="small" color="#0066CC" />
                <Text style={styles.loadMoreText}>Daha fazla yükleniyor...</Text>
              </View>
            ) : null
          )}
          contentContainerStyle={styles.listContainer}
        />
      )}

      {/* Bottom Action Bar */}
      {selectedUsers.size > 0 && (
        <View style={styles.bottomActionBar}>
          <TouchableOpacity
            style={[styles.actionButton, styles.followButton, processingBulkAction && styles.disabledButton]}
            onPress={handleBulkFollow}
            disabled={processingBulkAction}
          >
            {processingBulkAction ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Ionicons name="person-add" size={20} color="#fff" />
                <Text style={styles.actionButtonText}>Toplu Takip</Text>
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.unfollowButton, processingBulkAction && styles.disabledButton]}
            onPress={handleBulkUnfollow}
            disabled={processingBulkAction}
          >
            {processingBulkAction ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Ionicons name="person-remove" size={20} color="#fff" />
                <Text style={styles.actionButtonText}>Toplu İptal</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Filter Modal */}
      {renderFilterModal()}
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    paddingHorizontal: 15,
    paddingTop: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    backgroundColor: '#000',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: -34, // Merkezi hizalama için
  },
  filterButton: {
    padding: 5,
  },
  rightSpace: {
    width: 34,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 10,
  },
  searchContainer: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#000',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  searchInput: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    marginLeft: 10,
  },
  selectionControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    backgroundColor: '#111',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllText: {
    color: '#0066CC',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  selectedCount: {
    color: '#666',
    fontSize: 14,
  },
  listContainer: {
    paddingBottom: 100, // Bottom action bar için boşluk
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
    backgroundColor: '#000',
  },
  selectedUserItem: {
    backgroundColor: 'rgba(0, 102, 204, 0.1)',
    borderLeftWidth: 3,
    borderLeftColor: '#0066CC',
  },
  userItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#666',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkedBox: {
    backgroundColor: '#0066CC',
    borderColor: '#0066CC',
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#333',
  },
  userInfo: {
    flex: 1,
  },
  username: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  fullName: {
    color: '#999',
    fontSize: 14,
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusText: {
    color: '#666',
    fontSize: 13,
    marginLeft: 6,
  },
  followingText: {
    color: '#2ecc71',
  },
  pendingText: {
    color: '#f39c12',
  },
  userItemRight: {
    alignItems: 'flex-end',
  },
  popularityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularityText: {
    color: '#FFD700',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadMoreText: {
    color: '#666',
    fontSize: 14,
    marginLeft: 10,
  },
  bottomActionBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: '#111',
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#333',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 5,
  },
  followButton: {
    backgroundColor: '#0066CC',
  },
  unfollowButton: {
    backgroundColor: '#e74c3c',
  },
  disabledButton: {
    opacity: 0.6,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  modalCloseButton: {
    padding: 5,
  },
  filterSection: {
    marginBottom: 25,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0066CC',
    marginBottom: 15,
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 5,
  },
  filterOptionText: {
    color: '#fff',
    fontSize: 16,
  },
  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#222',
  },
  selectedSortOption: {
    backgroundColor: 'rgba(0, 102, 204, 0.2)',
    borderWidth: 1,
    borderColor: '#0066CC',
  },
  sortOptionText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 12,
  },
  selectedSortText: {
    color: '#0066CC',
    fontWeight: 'bold',
  },
  applyFiltersButton: {
    backgroundColor: '#0066CC',
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  applyFiltersButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default BulkFollowManagementScreen;
