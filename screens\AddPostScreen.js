import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import { collection, addDoc, serverTimestamp, doc, getDoc } from 'firebase/firestore';
import { auth, db } from '../firebase';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useToast } from '../contexts/ToastContext';

const AddPostScreen = () => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { translations } = useLanguage();
  const { showToast } = useToast();
  const [content, setContent] = useState('');
  const [postType, setPostType] = useState(translations.lyrics || 'Şarkı Sözü');
  const [loading, setLoading] = useState(false);
  const characterLimit = 700;

  const handlePost = async () => {
    if (!content.trim()) {
      showToast({
        message: translations.pleaseEnterContent || 'Lütfen gönderi içeriğinizi yazınız.',
        type: 'error'
      });
      return;
    }
    if (content.length > characterLimit) {
      showToast({
        message: translations.characterLimitExceeded?.replace('{limit}', characterLimit) || `Gönderi içeriği ${characterLimit} karakteri aşamaz.`,
        type: 'error'
      });
      return;
    }

    setLoading(true);

    try {
      // 1) Önce kullanıcıyı kontrol et
      if (!auth.currentUser) {
        throw new Error('Giriş yapılmamış!');
      }

      // 2) Firestore'daki users/{uid} dokümanını oku
      const userDocRef = doc(db, 'users', auth.currentUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      // 3) finalUsername -> Önce Firestore username, sonra displayName, en son "Kullanıcı"
      let finalUsername = 'Kullanıcı';

      if (userDocSnap.exists()) {
        const userData = userDocSnap.data();
        if (userData && userData.username) {
          finalUsername = userData.username;
        } else if (auth.currentUser.displayName) {
          finalUsername = auth.currentUser.displayName;
        }
      } else {
        // userDoc yoksa displayName varsa onu kullanalım
        if (auth.currentUser.displayName) {
          finalUsername = auth.currentUser.displayName;
        }
      }

      // 4) Post kaydet
      await addDoc(collection(db, 'posts'), {
        uid: auth.currentUser.uid,
        username: finalUsername, // <-- Firestore’daki username veya displayName veya "Kullanıcı"
        content: content.trim(),
        type: postType,
        createdAt: serverTimestamp(),
        likes: 0,
        likedBy: [],
      });

      Alert.alert('Başarılı', 'Gönderiniz paylaşıldı.');

      // Gönderi eklenince Ana Sayfa'ya git
      navigation.navigate('Ana Sayfa');

      setContent('');
    } catch (error) {
      console.error(error);
      Alert.alert('Hata', 'Gönderi paylaşılırken bir hata oluştu. ' + error.message);
    }

    setLoading(false);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <Text style={[styles.title, { color: theme.text }]}>{translations.addPost || "Gönderi Paylaş"}</Text>

      {/* Hakaret Uyarısı */}
      <View style={styles.warningContainer}>
        <Text style={styles.warningText}>
          {"Uyarı: Hakaret, küfür, nefret söylemi ve uygunsuz içerikler yasaktır. Bu tür içerikler kaldırılabilir veya hesabınız banlanabilir."}
        </Text>
      </View>

      <TextInput
        style={[styles.textInput, { backgroundColor: theme.cardBackground, color: theme.text }]}
        placeholder={translations.writeYourPost || "Gönderinizi buraya yazın..."}
        placeholderTextColor="#aaa"
        value={content}
        onChangeText={setContent}
        multiline
        maxLength={characterLimit}
        blurOnSubmit={false}
        returnKeyType="default"
        textAlignVertical="top"
      />
      <Text style={styles.charCount}>
        {content.length}/{characterLimit}
      </Text>

      <View style={styles.typeContainer}>
        <TouchableOpacity
          style={[
            styles.typeButton,
            postType === (translations.lyrics || 'Şarkı Sözü') && styles.selectedTypeButton,
          ]}
          onPress={() => setPostType(translations.lyrics || 'Şarkı Sözü')}
        >
          <Text
            style={[
              styles.typeButtonText,
              postType === (translations.lyrics || 'Şarkı Sözü') && styles.selectedTypeButtonText,
            ]}
          >
            {translations.lyrics || 'Şarkı Sözü'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.typeButton,
            postType === (translations.poem || 'Şiir') && styles.selectedTypeButton,
          ]}
          onPress={() => setPostType(translations.poem || 'Şiir')}
        >
          <Text
            style={[
              styles.typeButtonText,
              postType === (translations.poem || 'Şiir') && styles.selectedTypeButtonText,
            ]}
          >
            {translations.poem || 'Şiir'}
          </Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <ActivityIndicator size="large" color="#fff" style={{ marginVertical: 20 }} />
      ) : (
        <TouchableOpacity style={styles.postButton} onPress={handlePost}>
          <Text style={styles.postButtonText}>{translations.post || "Gönder"}</Text>
        </TouchableOpacity>
      )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default AddPostScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000', // Tema için dinamik olarak ayarlanıyor
    padding: 20,
    justifyContent: 'center',
  },
  warningContainer: {
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    borderRadius: 8,
    padding: 10,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 0, 0, 0.3)',
  },
  warningText: {
    color: '#ff6b6b',
    fontSize: 12,
    textAlign: 'center',
  },
  title: {
    fontSize: 28,
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  textInput: {
    backgroundColor: '#121212',
    color: '#fff',
    borderWidth: 1,
    borderColor: '#0066CC',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    height: 150,
    textAlignVertical: 'top',
  },
  charCount: {
    color: '#aaa',
    alignSelf: 'flex-end',
    marginTop: 5,
  },
  typeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 20,
  },
  typeButton: {
    flex: 1,
    backgroundColor: '#222',
    paddingVertical: 15,
    marginHorizontal: 5,
    borderRadius: 10,
    alignItems: 'center',
  },
  selectedTypeButton: {
    backgroundColor: '#0066CC',
  },
  typeButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  selectedTypeButtonText: {
    fontWeight: 'bold',
  },
  postButton: {
    backgroundColor: '#0066CC',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  postButtonText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
});
