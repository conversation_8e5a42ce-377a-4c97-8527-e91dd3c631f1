import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
  Keyboard,
  RefreshControl,
  Animated,
  LayoutAnimation,
  PanResponder,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  TouchableWithoutFeedback,
  StatusBar,
  Dimensions
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


import {
  collection,
  query,
  orderBy,
  addDoc,
  updateDoc,
  onSnapshot,
  serverTimestamp,
  doc,
  getDoc,
  getDocs,
  deleteDoc,
  increment,
  where
} from 'firebase/firestore';

import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { auth, db } from '../firebase';
import { createNotification, NOTIFICATION_TYPES } from '../utils/notificationUtils';
import { updateUserPopularity, listenToPostComments } from '../utils/popularityUtils';


// Avatar mapping
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

const COMMENT_CHAR_LIMIT = 200;
const MENTION_SUGGEST_LIMIT = 6;

/** "x dakika önce" şeklinde zaman dönüştürme */
function timeAgo(timestamp, timeNow) {
  if (!timestamp) return 'Bilinmeyen Tarih';
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  if (isNaN(date.getTime())) return 'Bilinmeyen Tarih';
  const diffSec = Math.floor((timeNow - date.getTime()) / 1000);
  if (diffSec < 0) return '0 saniye önce';
  if (diffSec < 60) return diffSec + ' saniye önce';
  if (diffSec < 3600) return Math.floor(diffSec / 60) + ' dakika önce';
  if (diffSec < 86400) return Math.floor(diffSec / 3600) + ' saat önce';
  return Math.floor(diffSec / 86400) + ' gün önce';
}

/** Kullanıcı profilini Firestore'dan çekme */
async function fetchUserProfile(uid) {
  if (!uid) return { username: 'Kullanıcı', photoURL: null, profilePic: null };
  try {
    const snap = await getDoc(doc(db, 'users', uid));
    if (snap.exists()) {
      const d = snap.data();
      return {
        username: d.username || 'Kullanıcı',
        photoURL: d.photoURL || null, // Gerçek fotoğraf
        profilePic: d.profilePic || null // Seçili avatar
      };
    }
  } catch (e) {
    console.log('fetchUserProfile err:', e);
  }
  return { username: 'Kullanıcı', photoURL: null, profilePic: null };
}

/** @mention metnini mavi renkle parse edip gösterir */
function parseAndRenderText(text, maxLines = 8, showFull = false, onToggle = null) {
  if (!text) return null;
  if (typeof text !== 'string') {
    return <Text style={{ color: '#fff', fontSize: 16 }}>{text}</Text>;
  }

  // Önce satırlara böl
  const lines = text.split('\n');
  const shouldTruncate = lines.length > maxLines && !showFull;
  const displayLines = shouldTruncate ? lines.slice(0, maxLines) : lines;

  return (
    <View>
      <Text style={{ color: '#fff', fontSize: 16 }}>
        {displayLines.map((line, lineIdx) => (
          <Text key={lineIdx}>
            {line.split(/\s+/).map((part, partIdx) => {
              if (part.startsWith('@') && part.length > 1) {
                return (
                  <Text key={partIdx} style={{ color: '#3498db', fontSize: 16 }}>
                    {part + ' '}
                  </Text>
                );
              }
              return part + ' ';
            })}
            {lineIdx < displayLines.length - 1 && '\n'}
          </Text>
        ))}
      </Text>
      {lines.length > maxLines && onToggle && (
        <TouchableOpacity style={{ marginTop: 4 }} onPress={onToggle}>
          <Text style={{ color: '#3498db', fontSize: 14 }}>
            {showFull ? 'Daha az göster' : 'Devamını oku...'}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

export default function CommentsScreen() {
  const route = useRoute();
  const navigation = useNavigation();
  const { postId } = route.params || {};
  const insets = useSafeAreaInsets();

  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeNow, setTimeNow] = useState(Date.now());
  const [refreshing, setRefreshing] = useState(false);

  const [expanded, setExpanded] = useState({});
  const [commentText, setCommentText] = useState('');

  // replyMode: { commentId, username, isNested, replyId }
  const [replyMode, setReplyMode] = useState(null);

  // Beğeni işlemi için debounce
  const [likingComments, setLikingComments] = useState(new Set());

  // Engellenen kullanıcılar
  const [blockedUsers, setBlockedUsers] = useState([]);

  const [showMentionList, setShowMentionList] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [mentionUsers, setMentionUsers] = useState([]);
  const mentionTimer = useRef(null);

  const inputRef = useRef(null);
  const flatListRef = useRef(null);

  // Engellenen kullanıcıları yükle
  useEffect(() => {
    const loadBlockedUsers = async () => {
      if (!auth.currentUser) return;

      try {
        const userRef = doc(db, 'users', auth.currentUser.uid);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          setBlockedUsers(userData.blockedUsers || []);
        }
      } catch (error) {
        console.error('Engellenen kullanıcılar yüklenemedi:', error);
      }
    };

    loadBlockedUsers();
  }, []);

  useEffect(() => {
    if (!postId) {
      setLoading(false);
      return;
    }
    loadCommentsRealtime();
  }, [postId]);



  function loadCommentsRealtime() {
    setLoading(true);
    const cRef = collection(db, 'posts', postId, 'comments');
    const q = query(cRef, orderBy('createdAt', 'desc'));
    const unsub = onSnapshot(q, async (snapshot) => {
      // Sadece değişiklik varsa işle
      if (snapshot.metadata.hasPendingWrites) return;

      const arr = [];
      const seenIds = new Set();

      // Batch olarak user profile'ları çek
      const userPromises = new Map();

      for (let ds of snapshot.docs) {
        // Duplicate ID kontrolü
        if (seenIds.has(ds.id)) {
          console.warn('Duplicate comment ID found:', ds.id);
          continue;
        }
        seenIds.add(ds.id);

        const d = ds.data();

        // Engellenen kullanıcıların yorumlarını filtrele
        if (blockedUsers.includes(d.uid)) {
          continue;
        }

        // User profile'ı cache'le
        if (!userPromises.has(d.uid)) {
          userPromises.set(d.uid, fetchUserProfile(d.uid));
        }

        const up = await userPromises.get(d.uid);
        arr.push({
          id: ds.id,
          postId,
          ...d,
          username: up.username,
          userPhoto: up.photoURL,
          profilePic: up.profilePic
        });
      }

      // Batch update
      requestAnimationFrame(() => {
        setComments(arr);
      });
      setLoading(false);
      setRefreshing(false);

      // Gönderi dokümanındaki yorum sayısını güncelle
      try {
        const postRef = doc(db, 'posts', postId);
        await updateDoc(postRef, {
          commentsCount: snapshot.size
        });

        // Gönderi sahibinin popülerlik puanını güncelle
        const postSnap = await getDoc(postRef);
        if (postSnap.exists()) {
          const postData = postSnap.data();
          await updateUserPopularity(postData.uid, false);
        }
      } catch (error) {
        console.error('Yorum sayısı güncelleme hatası:', error);
      }
    });

    // Gönderi yorum sayısını dinle
    listenToPostComments(postId, (commentCount) => {
      console.log(`Gönderi ${postId} yorum sayısı: ${commentCount}`);
    });

    return () => unsub();
  }

  function onRefresh() {
    setRefreshing(true);
    setTimeNow(Date.now());
    setTimeout(() => setRefreshing(false), 700);
  }

  function handleGoBack() {
    navigation.goBack();
  }

  // @mention algılaması
  useEffect(() => {
    const lastChar = commentText.slice(-1);
    if (lastChar === '@') {
      setShowMentionList(true);
      setMentionQuery('');
    } else if (showMentionList) {
      const parted = commentText.split('@');
      if (parted.length > 1) {
        const afterAt = parted[parted.length - 1];
        const spaceIdx = afterAt.search(/[\s.,:;!?\n]/);
        if (spaceIdx >= 0) {
          setShowMentionList(false);
        } else {
          setMentionQuery(afterAt.toLowerCase());
        }
      } else {
        setShowMentionList(false);
      }
    }
  }, [commentText]);

  useEffect(() => {
    if (!showMentionList) return;
    if (!mentionQuery) {
      setMentionUsers([]);
      return;
    }
    if (mentionTimer.current) {
      clearTimeout(mentionTimer.current);
    }
    mentionTimer.current = setTimeout(() => {
      searchMention(mentionQuery);
    }, 300);
  }, [mentionQuery, showMentionList]);

  function searchMention(q) {
    if (!q) return;
    onSnapshot(collection(db, 'users'), (snap) => {
      let allUsers = snap.docs.map(d => ({ id: d.id, ...d.data() }));
      let filtered = allUsers.filter(u =>
        (u.username || '').toLowerCase().includes(q) &&
        !blockedUsers.includes(u.id) // Engellenen kullanıcıları filtrele
      );
      filtered = filtered.slice(0, MENTION_SUGGEST_LIMIT);
      setMentionUsers(filtered);
    });
  }

  function handleSelectMentionUser(u) {
    const parted = commentText.split('@');
    parted.pop();
    const newTxt = parted.join('@') + '@' + u.username + ' ';
    setCommentText(newTxt);
    setShowMentionList(false);
  }

  // Yorum veya cevaba cevap
  function handleReplyPress(id, username, isNested = false, replyId = null) {
    setReplyMode({ commentId: id, username, isNested, replyId });
    setCommentText('@' + username + ' ');
    if (inputRef.current) {
      setTimeout(() => inputRef.current.focus(), 50);
    }
  }

  async function handleSend() {
    let tx = commentText;
    if (!tx.trim()) return;

    // Optimistic UI update - hemen input'u temizle
    setCommentText('');

    if (tx.length > COMMENT_CHAR_LIMIT) {
      tx = tx.substring(0, COMMENT_CHAR_LIMIT) + '...';
    }

    try {
      if (replyMode) {
        if (replyMode.isNested) {
          await addNestedReply(replyMode.commentId, replyMode.replyId, tx, replyMode.username);
        } else {
          await addReply(replyMode.commentId, tx, replyMode.username);
        }
        setReplyMode(null);
      } else {
        await addComment(tx);
      }

      // Yorum gönderildikten sonra liste başına scroll (en yeni yorum üstte)
      requestAnimationFrame(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
      });

    } catch (error) {
      console.error('Yorum gönderme hatası:', error);
      // Hata durumunda text'i geri koy
      setCommentText(commentText);
    }
  }

  async function addComment(txt) {
    if (!postId) return;
    try {
      const up = await fetchUserProfile(auth.currentUser?.uid);

      // Önce gönderi sahibini bul
      const postRef = doc(db, 'posts', postId);
      const postSnap = await getDoc(postRef);

      if (!postSnap.exists()) {
        console.log('Gönderi bulunamadı');
        return;
      }

      const postData = postSnap.data();
      const postOwnerId = postData.uid;

      // Engelleme kontrolü - gönderi sahibi bizi engellemiş mi?
      const postOwnerRef = doc(db, 'users', postOwnerId);
      const postOwnerDoc = await getDoc(postOwnerRef);

      if (postOwnerDoc.exists()) {
        const postOwnerData = postOwnerDoc.data();
        const postOwnerBlockedUsers = postOwnerData.blockedUsers || [];

        if (postOwnerBlockedUsers.includes(auth.currentUser?.uid)) {
          Alert.alert('Uyarı', 'Bu kullanıcı sizi engellediği için yorum yapamazsınız.');
          return;
        }
      }

      // Biz gönderi sahibini engellemiş miyiz?
      if (blockedUsers.includes(postOwnerId)) {
        Alert.alert('Uyarı', 'Engellediğiniz kullanıcının gönderisine yorum yapamazsınız.');
        return;
      }

      // Yorumu ekle
      const commentRef = await addDoc(collection(db, 'posts', postId, 'comments'), {
        uid: auth.currentUser?.uid,
        username: up.username,
        userPhoto: up.photoURL,
        profilePic: up.profilePic,
        comment: txt,
        createdAt: serverTimestamp(),
        likes: 0,
        likedBy: [],
        edited: false
      });

      // Gönderi dokümanındaki yorum sayısını artır
      await updateDoc(postRef, {
        commentsCount: increment(1)
      });

      // Yorum bildirimi oluştur (kendi gönderisine yorum yapmışsa bildirim gönderme)
      if (auth.currentUser?.uid !== postOwnerId) {
        await createNotification(
          NOTIFICATION_TYPES.COMMENT,
          auth.currentUser?.uid,
          postOwnerId,
          postId,
          null,
          null,
          txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
        );


      }

      // Etiketlenen kullanıcılara bildirim gönder
      const mentionedUsers = txt.match(/@(\w+)/g);
      if (mentionedUsers && mentionedUsers.length > 0) {
        for (const mention of mentionedUsers) {
          const username = mention.substring(1); // @ işaretini kaldır

          // Kullanıcıyı bul
          const usersRef = collection(db, 'users');
          const q = query(usersRef, where('username', '==', username));
          const userSnapshot = await getDocs(q);

          if (!userSnapshot.empty) {
            const mentionedUser = userSnapshot.docs[0];
            const mentionedUserId = mentionedUser.id;

            // Kendine etiketleme bildirimi gönderme ve engelleme kontrolü
            if (mentionedUserId !== auth.currentUser?.uid &&
                !blockedUsers.includes(mentionedUserId)) {

              // Etiketlenen kullanıcının bizi engellemiş olup olmadığını kontrol et
              const mentionedUserData = mentionedUser.data();
              const mentionedUserBlockedUsers = mentionedUserData.blockedUsers || [];

              if (!mentionedUserBlockedUsers.includes(auth.currentUser?.uid)) {
                await createNotification(
                  NOTIFICATION_TYPES.MENTION,
                  auth.currentUser?.uid,
                  mentionedUserId,
                  postId,
                  null,
                  null,
                  txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
                );
              }
            }
          }
        }
      }

      // Gönderi yorum sayısını dinle
      listenToPostComments(postId, (commentCount) => {
        console.log(`Gönderi ${postId} yorum sayısı güncellendi: ${commentCount}`);
        // Burada UI'ı güncelleyebilirsiniz
      });

      // Gönderi sahibinin popülerlik puanını güncelle
      try {
        await updateUserPopularity(postOwnerId, true);
      } catch (popError) {
        console.error('Popülerlik güncelleme hatası:', popError);
      }
    } catch (err) {
      console.log('addComment err:', err);
    }
  }

  async function addReply(parentId, txt, parentUsername) {
    if (!postId) return;
    try {
      const up = await fetchUserProfile(auth.currentUser?.uid);

      // Yorum sahibini bul
      const commentRef = doc(db, 'posts', postId, 'comments', parentId);
      const commentSnap = await getDoc(commentRef);

      if (!commentSnap.exists()) {
        console.log('Yorum bulunamadı');
        return;
      }

      const commentData = commentSnap.data();
      const commentOwnerId = commentData.uid;

      // Engelleme kontrolü - yorum sahibi bizi engellemiş mi?
      const commentOwnerRef = doc(db, 'users', commentOwnerId);
      const commentOwnerDoc = await getDoc(commentOwnerRef);

      if (commentOwnerDoc.exists()) {
        const commentOwnerData = commentOwnerDoc.data();
        const commentOwnerBlockedUsers = commentOwnerData.blockedUsers || [];

        if (commentOwnerBlockedUsers.includes(auth.currentUser?.uid)) {
          Alert.alert('Uyarı', 'Bu kullanıcı sizi engellediği için cevap veremezsiniz.');
          return;
        }
      }

      // Biz yorum sahibini engellemiş miyiz?
      if (blockedUsers.includes(commentOwnerId)) {
        Alert.alert('Uyarı', 'Engellediğiniz kullanıcının yorumuna cevap veremezsiniz.');
        return;
      }

      // Cevabı ekle
      await addDoc(
        collection(db, 'posts', postId, 'comments', parentId, 'replies'),
        {
          uid: auth.currentUser?.uid,
          username: up.username,
          userPhoto: up.photoURL,
          profilePic: up.profilePic,
          reply: txt,
          replyToUser: parentUsername,
          createdAt: serverTimestamp(),
          likes: 0,
          likedBy: [],
          edited: false
        }
      );

      // Cevap bildirimi oluştur (kendi yorumuna cevap vermişse bildirim gönderme)
      if (auth.currentUser?.uid !== commentOwnerId) {
        await createNotification(
          NOTIFICATION_TYPES.REPLY,
          auth.currentUser?.uid,
          commentOwnerId,
          postId,
          parentId,
          null,
          txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
        );
      }

      // Etiketlenen kullanıcılara bildirim gönder
      const mentionedUsers = txt.match(/@(\w+)/g);
      if (mentionedUsers && mentionedUsers.length > 0) {
        for (const mention of mentionedUsers) {
          const username = mention.substring(1); // @ işaretini kaldır

          // Kullanıcıyı bul
          const usersRef = collection(db, 'users');
          const q = query(usersRef, where('username', '==', username));
          const userSnapshot = await getDocs(q);

          if (!userSnapshot.empty) {
            const mentionedUser = userSnapshot.docs[0];
            const mentionedUserId = mentionedUser.id;

            // Kendine etiketleme bildirimi gönderme ve engelleme kontrolü
            if (mentionedUserId !== auth.currentUser?.uid &&
                !blockedUsers.includes(mentionedUserId)) {

              // Etiketlenen kullanıcının bizi engellemiş olup olmadığını kontrol et
              const mentionedUserData = mentionedUser.data();
              const mentionedUserBlockedUsers = mentionedUserData.blockedUsers || [];

              if (!mentionedUserBlockedUsers.includes(auth.currentUser?.uid)) {
                await createNotification(
                  NOTIFICATION_TYPES.MENTION,
                  auth.currentUser?.uid,
                  mentionedUserId,
                  postId,
                  parentId,
                  null,
                  txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
                );
              }
            }
          }
        }
      }
    } catch (err) {
      console.log('addReply err:', err);
    }
  }

  async function addNestedReply(commentId, replyId, txt, parentUsername) {
    if (!postId) return;
    try {
      const up = await fetchUserProfile(auth.currentUser?.uid);

      // Cevap sahibini bul
      const replyRef = doc(db, 'posts', postId, 'comments', commentId, 'replies', replyId);
      const replySnap = await getDoc(replyRef);

      if (!replySnap.exists()) {
        console.log('Cevap bulunamadı');
        return;
      }

      const replyData = replySnap.data();
      const replyOwnerId = replyData.uid;

      // Engelleme kontrolü - cevap sahibi bizi engellemiş mi?
      const replyOwnerRef = doc(db, 'users', replyOwnerId);
      const replyOwnerDoc = await getDoc(replyOwnerRef);

      if (replyOwnerDoc.exists()) {
        const replyOwnerData = replyOwnerDoc.data();
        const replyOwnerBlockedUsers = replyOwnerData.blockedUsers || [];

        if (replyOwnerBlockedUsers.includes(auth.currentUser?.uid)) {
          Alert.alert('Uyarı', 'Bu kullanıcı sizi engellediği için cevap veremezsiniz.');
          return;
        }
      }

      // Biz cevap sahibini engellemiş miyiz?
      if (blockedUsers.includes(replyOwnerId)) {
        Alert.alert('Uyarı', 'Engellediğiniz kullanıcının cevabına yanıt veremezsiniz.');
        return;
      }

      // Cevabı ekle
      await addDoc(
        collection(db, 'posts', postId, 'comments', commentId, 'replies'),
        {
          uid: auth.currentUser?.uid,
          username: up.username,
          userPhoto: up.photoURL,
          profilePic: up.profilePic,
          reply: txt,
          replyToUser: parentUsername,
          parentReplyId: replyId,
          createdAt: serverTimestamp(),
          likes: 0,
          likedBy: [],
          edited: false
        }
      );

      // Cevap bildirimi oluştur (kendi cevabına cevap vermişse bildirim gönderme)
      if (auth.currentUser?.uid !== replyOwnerId) {
        await createNotification(
          NOTIFICATION_TYPES.REPLY,
          auth.currentUser?.uid,
          replyOwnerId,
          postId,
          commentId,
          replyId,
          txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
        );
      }

      // Etiketlenen kullanıcılara bildirim gönder
      const mentionedUsers = txt.match(/@(\w+)/g);
      if (mentionedUsers && mentionedUsers.length > 0) {
        for (const mention of mentionedUsers) {
          const username = mention.substring(1); // @ işaretini kaldır

          // Kullanıcıyı bul
          const usersRef = collection(db, 'users');
          const q = query(usersRef, where('username', '==', username));
          const userSnapshot = await getDocs(q);

          if (!userSnapshot.empty) {
            const mentionedUser = userSnapshot.docs[0];
            const mentionedUserId = mentionedUser.id;

            // Kendine etiketleme bildirimi gönderme ve engelleme kontrolü
            if (mentionedUserId !== auth.currentUser?.uid &&
                !blockedUsers.includes(mentionedUserId)) {

              // Etiketlenen kullanıcının bizi engellemiş olup olmadığını kontrol et
              const mentionedUserData = mentionedUser.data();
              const mentionedUserBlockedUsers = mentionedUserData.blockedUsers || [];

              if (!mentionedUserBlockedUsers.includes(auth.currentUser?.uid)) {
                await createNotification(
                  NOTIFICATION_TYPES.MENTION,
                  auth.currentUser?.uid,
                  mentionedUserId,
                  postId,
                  commentId,
                  replyId,
                  txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
                );
              }
            }
          }
        }
      }

      // Etiketlenen kullanıcılara bildirim gönder
      const mentionedUsersInReply = txt.match(/@(\w+)/g);
      if (mentionedUsersInReply && mentionedUsersInReply.length > 0) {
        for (const mention of mentionedUsersInReply) {
          const username = mention.substring(1); // @ işaretini kaldır

          // Kullanıcıyı bul
          const usersRef = collection(db, 'users');
          const q = query(usersRef, where('username', '==', username));
          const userSnapshot = await getDocs(q);

          if (!userSnapshot.empty) {
            const mentionedUser = userSnapshot.docs[0];
            const mentionedUserId = mentionedUser.id;

            // Kendine etiketleme bildirimi gönderme ve engelleme kontrolü
            if (mentionedUserId !== auth.currentUser?.uid &&
                !blockedUsers.includes(mentionedUserId)) {

              // Etiketlenen kullanıcının bizi engellemiş olup olmadığını kontrol et
              const mentionedUserData = mentionedUser.data();
              const mentionedUserBlockedUsers = mentionedUserData.blockedUsers || [];

              if (!mentionedUserBlockedUsers.includes(auth.currentUser?.uid)) {
                await createNotification(
                  NOTIFICATION_TYPES.MENTION,
                  auth.currentUser?.uid,
                  mentionedUserId,
                  postId,
                  commentId,
                  replyId,
                  txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
                );
              }
            }
          }
        }
      }
    } catch (err) {
      console.log('addNestedReply err:', err);
    }
  }

  async function toggleCommentLike(commentId) {
    if (!auth.currentUser) return;

    // Eğer bu yorum zaten işlem görüyorsa, çık
    if (likingComments.has(commentId)) return;

    const userId = auth.currentUser.uid;

    // Yorum sahibini bul ve engelleme kontrolü yap
    const comment = comments.find(c => c.id === commentId);
    if (!comment) return;

    // Engelleme kontrolü
    if (blockedUsers.includes(comment.uid)) {
      Alert.alert('Uyarı', 'Engellediğiniz kullanıcının yorumunu beğenemezsiniz.');
      return;
    }

    // Yorum sahibi bizi engellemiş mi kontrol et
    try {
      const commentOwnerRef = doc(db, 'users', comment.uid);
      const commentOwnerDoc = await getDoc(commentOwnerRef);

      if (commentOwnerDoc.exists()) {
        const commentOwnerData = commentOwnerDoc.data();
        const commentOwnerBlockedUsers = commentOwnerData.blockedUsers || [];

        if (commentOwnerBlockedUsers.includes(userId)) {
          Alert.alert('Uyarı', 'Bu kullanıcı sizi engellediği için yorumunu beğenemezsiniz.');
          return;
        }
      }
    } catch (error) {
      console.error('Engelleme kontrolü hatası:', error);
    }

    // İşlem başladığını işaretle
    setLikingComments(prev => new Set([...prev, commentId]));
    setComments(prev =>
      prev.map(c => {
        if (c.id === commentId) {
          const isLiked = c.likedBy?.includes(userId);
          const newLikes = isLiked ? (c.likes || 0) - 1 : (c.likes || 0) + 1;
          const newLikedBy = isLiked
            ? c.likedBy.filter(x => x !== userId)
            : [...(c.likedBy || []), userId];
          return { ...c, likes: newLikes, likedBy: newLikedBy };
        }
        return c;
      })
    );
    try {
      const docRef = doc(db, 'posts', postId, 'comments', commentId);
      const snap = await getDoc(docRef);
      if (!snap.exists()) return;
      const data = snap.data();
      const isLiked = data.likedBy?.includes(userId);
      await updateDoc(docRef, {
        likes: isLiked ? data.likes - 1 : data.likes + 1,
        likedBy: isLiked
          ? data.likedBy.filter(x => x !== userId)
          : [...(data.likedBy || []), userId]
      });
    } catch (err) {
      console.log('toggleCommentLike err:', err);
      // Hata durumunda UI'ı geri al
      setComments(prev =>
        prev.map(c => {
          if (c.id === commentId) {
            const isLiked = c.likedBy?.includes(userId);
            const newLikes = isLiked ? (c.likes || 0) + 1 : (c.likes || 0) - 1;
            const newLikedBy = isLiked
              ? [...(c.likedBy || []), userId]
              : c.likedBy.filter(x => x !== userId);
            return { ...c, likes: newLikes, likedBy: newLikedBy };
          }
          return c;
        })
      );
    } finally {
      // İşlem bittiğini işaretle
      setLikingComments(prev => {
        const newSet = new Set(prev);
        newSet.delete(commentId);
        return newSet;
      });
    }
  }

  function handleShowCommentLikers(comment) {
    if (!comment.likedBy || comment.likedBy.length === 0) {
      Alert.alert('Bilgi', 'Bu yorumu beğenen yok.');
      return;
    }
    navigation.navigate('LikersScreen', { likedBy: comment.likedBy });
  }

  function handleEditRequest(c) {
    if (c.uid !== auth.currentUser?.uid) {
      Alert.alert('Hata', 'Sadece kendi yorumunuzu düzenleyebilirsiniz.');
      return;
    }
    setComments(prev =>
      prev.map(x => {
        if (x.id === c.id) {
          return { ...x, editMode: true, editText: x.comment };
        }
        return x;
      })
    );
  }

  function handleCancelEdit(commentId) {
    setComments(prev =>
      prev.map(x => {
        if (x.id === commentId) {
          const { editMode, editText, ...rest } = x;
          return rest;
        }
        return x;
      })
    );
  }

  async function handleSaveEdit(commentId) {
    const c = comments.find(x => x.id === commentId);
    if (!c) return;
    let newText = (c.editText || '').trim();
    if (!newText) {
      Alert.alert('Uyarı', 'Boş yorum olamaz.');
      return;
    }
    if (newText.length > COMMENT_CHAR_LIMIT) {
      newText = newText.substring(0, COMMENT_CHAR_LIMIT) + '...';
    }
    try {
      await updateDoc(doc(db, 'posts', postId, 'comments', commentId), {
        comment: newText,
        edited: true
      });
      setComments(prev =>
        prev.map(y => {
          if (y.id === commentId) {
            return {
              ...y,
              comment: newText,
              edited: true,
              editMode: false,
              editText: undefined
            };
          }
          return y;
        })
      );
    } catch (err) {
      Alert.alert('Hata', 'Düzenleme başarısız: ' + err.message);
    }
  }

  async function handleDeleteComment(c) {
    if (c.uid !== auth.currentUser?.uid) {
      Alert.alert('Hata', 'Sadece kendi yorumunuzu silebilirsiniz.');
      return;
    }
    Alert.alert('Sil', 'Bu yorumu silmek istiyor musunuz?', [
      { text: 'İptal', style: 'cancel' },
      {
        text: 'Sil',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteDoc(doc(db, 'posts', postId, 'comments', c.id));
          } catch (err) {
            console.log('deleteComment err:', err);
          }
        }
      }
    ]);
  }

  function toggleReplies(commentId) {
    setExpanded(prev => {
      const newVal = { ...prev, [commentId]: !prev[commentId] };
      // Replies açıldığında otomatik en alta kaydırmak isterseniz:
      // if (newVal[commentId] && flatListRef.current) {
      //   setTimeout(() => {
      //     flatListRef.current.scrollToEnd({ animated: true });
      //   }, 100);
      // }
      return newVal;
    });
  }

  function renderItem({ item }) {
    return (
      <CommentItem
        comment={item}
        expanded={expanded}
        toggleReplies={toggleReplies}
        handleReplyPress={handleReplyPress}
        toggleCommentLike={toggleCommentLike}
        handleShowCommentLikers={handleShowCommentLikers}
        handleEditRequest={handleEditRequest}
        handleCancelEdit={handleCancelEdit}
        handleSaveEdit={handleSaveEdit}
        handleDeleteComment={handleDeleteComment}
        timeNow={timeNow}
      />
    );
  }

  if (!postId) {
    return (
      <SafeAreaView style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ color: '#fff' }}>Gönderi Bulunamadı.</Text>
      </SafeAreaView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { paddingTop: insets.top }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor="#000"
        translucent={false}
      />

      {/* Üst Header - Sabit */}
      <View style={styles.topHeader}>
        <TouchableOpacity onPress={handleGoBack} style={{ marginRight: 10 }}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Yorumlar</Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Ana içerik alanı */}
      <View style={{ flex: 1 }}>
        {/* İçerik alanı */}
        {loading ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <ActivityIndicator size="large" color="#fff" />
          </View>
        ) : (
          <View style={{ flex: 1 }}>
            <FlatList
              ref={flatListRef}
              data={comments}
              keyExtractor={(item, index) => item.id || `comment-${index}`}
              renderItem={renderItem}
              keyboardShouldPersistTaps="always"
              keyboardDismissMode="on-drag"
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />
              }
              ListEmptyComponent={
                <Text style={styles.emptyListText}>Henüz yorum yok.</Text>
              }
              contentContainerStyle={{
                paddingBottom: 20,
                flexGrow: 1
              }}
              showsVerticalScrollIndicator={false}
              removeClippedSubviews={true}
              maxToRenderPerBatch={3}
              windowSize={3}
              initialNumToRender={5}
              updateCellsBatchingPeriod={50}
              getItemLayout={(data, index) => ({
                length: 120, // Daha gerçekçi yorum yüksekliği
                offset: 120 * index,
                index,
              })}
              disableVirtualization={false}
              legacyImplementation={false}
            />

            {/* @mention listesi */}
            {showMentionList && mentionUsers.length > 0 && (
              <MentionList mentionUsers={mentionUsers} onSelect={handleSelectMentionUser} />
            )}

            {/* Cevap Banner */}
            {replyMode && (
              <View style={styles.replyBanner}>
                <Text style={styles.replyBannerText}>
                  {replyMode.isNested
                    ? `@${replyMode.username} adlı cevaba cevap`
                    : `@${replyMode.username} adlı kişiye cevap`}
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    setReplyMode(null);
                    setCommentText('');
                  }}
                >
                  <Ionicons name="close-circle" size={20} color="#fff" />
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}
      </View>

      {/* Alt kısımdaki Yorum Yaz Input - Klavye ile birlikte hareket eder */}
      <View style={[
        styles.inputBar,
        {
          paddingBottom: Math.max(insets.bottom, 5),
        }
      ]}>
        <TextInput
          ref={inputRef}
          style={styles.input}
          placeholder={
            replyMode
              ? replyMode.isNested
                ? 'Cevap yaz (nested)...'
                : 'Cevap yaz...'
              : 'Yorum yaz...'
          }
          placeholderTextColor="#aaa"
          multiline={true}
          textAlignVertical={Platform.OS === 'android' ? 'top' : undefined}
          scrollEnabled={true}
          maxLength={COMMENT_CHAR_LIMIT}
          value={commentText}
          onChangeText={setCommentText}
          onFocus={() => {
            // Daha hızlı scroll
            setTimeout(() => {
              flatListRef.current?.scrollToEnd({ animated: true });
            }, 100);
          }}
          returnKeyType="default"
          enablesReturnKeyAutomatically={false}
          blurOnSubmit={false}
        />
        {commentText.length > 0 && (
          <Text style={styles.charCounter}>
            {commentText.length}/{COMMENT_CHAR_LIMIT}
          </Text>
        )}
        <TouchableOpacity
          style={[styles.sendBtn, { opacity: commentText.trim() ? 1 : 0.5 }]}
          onPress={handleSend}
          disabled={!commentText.trim()}
        >
          <Ionicons name="send" size={20} color="#fff" />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

/** Tekil Yorum Bileşeni - Memoized */
const CommentItem = React.memo(function CommentItem({
  comment,
  expanded,
  toggleReplies,
  handleReplyPress,
  toggleCommentLike,
  handleShowCommentLikers,
  handleEditRequest,
  handleCancelEdit,
  handleSaveEdit,
  handleDeleteComment,
  timeNow
}) {
  const [deleted, setDeleted] = useState(false);
  const [showFullComment, setShowFullComment] = useState(false);
  const panX = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();

  const isOwner = comment.uid === auth.currentUser?.uid;
  const isLiked = comment.likedBy?.includes(auth.currentUser?.uid);
  const editMode = comment.editMode || false;
  const [editText, setEditText] = useState(comment.editText || '');

  const [repliesCount, setRepliesCount] = useState(0);
  useEffect(() => {
    if (!comment.id || !comment.postId) return;
    const rRef = collection(db, 'posts', comment.postId, 'comments', comment.id, 'replies');
    const q = query(rRef, orderBy('createdAt', 'asc'));
    const unsub = onSnapshot(q, snap => {
      setRepliesCount(snap.size);
    });
    return () => unsub();
  }, [comment.id, comment.postId]);

  useEffect(() => {
    if (editMode) {
      setEditText(comment.editText || comment.comment || '');
    }
  }, [editMode]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, g) => isOwner && Math.abs(g.dx) > 15,
      onPanResponderMove: (_, g) => {
        panX.setValue(g.dx);
      },
      onPanResponderRelease: async (_, g) => {
        if (g.dx < -120 && isOwner) {
          LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
          setDeleted(true);
          await handleDeleteComment(comment);
        } else {
          Animated.spring(panX, {
            toValue: 0,
            useNativeDriver: true
          }).start();
        }
      }
    })
  ).current;
  if (deleted) return null;

  const timeTxt = comment.createdAt ? timeAgo(comment.createdAt, timeNow) : '';
  const showEditedLabel = comment.edited && isOwner && !editMode;

  function goProfile() {
    navigation.navigate('OtherProfileScreen', { uid: comment.uid });
  }

  function handleSave() {
    if (!editText.trim()) {
      Alert.alert('Uyarı', 'Boş yorum olamaz');
      return;
    }
    comment.editText = editText;
    handleSaveEdit(comment.id);
  }

  return (
    <Animated.View
      style={[styles.commentBox, { transform: [{ translateX: panX }] }]}
      {...(isOwner ? panResponder.panHandlers : {})}
    >
      <View style={styles.commentHeader}>
        <TouchableOpacity style={styles.commentLeft} onPress={goProfile}>
          <Image
            source={
              comment.profilePic && avatarMap[comment.profilePic]
                ? avatarMap[comment.profilePic]
                : comment.userPhoto
                  ? { uri: comment.userPhoto }
                  : require('../assets/default-avatar.png')
            }
            style={styles.avatar}
            defaultSource={require('../assets/default-avatar.png')}
            fadeDuration={0}
            resizeMode="cover"
          />
          <Text style={styles.username}>{comment.username}</Text>
        </TouchableOpacity>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {showEditedLabel && <Text style={styles.editedLabel}>Düzenlendi</Text>}
          {isOwner && !editMode && (
            <>
              <TouchableOpacity
                style={{ marginHorizontal: 10 }}
                onPress={() => handleEditRequest(comment)}
              >
                <Ionicons name="create-outline" size={18} color="#fff" />
              </TouchableOpacity>
              <TouchableOpacity onPress={() => handleDeleteComment(comment)}>
                <Ionicons name="trash-outline" size={18} color="#f44" />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      {editMode ? (
        <View style={styles.editContainer}>
          <TextInput
            style={styles.editInput}
            value={editText}
            multiline
            onChangeText={(val) => {
              if (val.length > COMMENT_CHAR_LIMIT) {
                val = val.substring(0, COMMENT_CHAR_LIMIT) + '...';
              }
              setEditText(val);
            }}
          />
          <View style={styles.editButtonsRow}>
            <TouchableOpacity
              style={styles.cancelBtn}
              onPress={() => handleCancelEdit(comment.id)}
            >
              <Ionicons name="close" size={16} color="#fff" style={{ marginRight: 4 }} />
              <Text style={styles.cancelBtnText}>Vazgeç</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveBtn} onPress={handleSave}>
              <Ionicons name="checkmark-done" size={16} color="#fff" style={{ marginRight: 4 }} />
              <Text style={styles.saveBtnText}>Kaydet</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.commentContent}>
          {parseAndRenderText(
            comment.comment,
            8,
            showFullComment,
            () => setShowFullComment(!showFullComment)
          )}
        </View>
      )}

      <View style={[styles.commentFooter, { justifyContent: 'space-between' }]}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            style={styles.actionBtn}
            onPress={() => toggleCommentLike(comment.id)}
            onLongPress={() => handleShowCommentLikers(comment)}
          >
            <Ionicons
              name={isLiked ? 'heart' : 'heart-outline'}
              size={22}
              color={isLiked ? '#e74c3c' : '#fff'}
            />
            <Text style={styles.actionLabel}>{comment.likes || 0}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionBtn}
            onPress={() => handleReplyPress(comment.id, comment.username)}
          >
            <Ionicons name="arrow-undo-outline" size={22} color="#fff" />
            <Text style={styles.actionLabel}>Cevap</Text>
          </TouchableOpacity>
          {repliesCount > 0 && (
            <TouchableOpacity style={styles.toggleRepliesBtn} onPress={() => toggleReplies(comment.id)}>
              <Text style={styles.toggleRepliesText}>
                {expanded[comment.id] ? 'Cevapları Gizle' : `${repliesCount} yanıtı görüntüle`}
              </Text>
            </TouchableOpacity>
          )}
        </View>
        <Text style={styles.timeText}>{timeTxt}</Text>
      </View>

      <RepliesPreview
        parentComment={comment}
        expanded={expanded[comment.id]}
        toggleReplies={toggleReplies}
        timeNow={timeNow}
        handleReplyPress={handleReplyPress}
      />
    </Animated.View>
  );
});

/** RepliesPreview: alt cevapları yükler */
function RepliesPreview({ parentComment, expanded, toggleReplies, timeNow, handleReplyPress }) {
  const { id: parentId, postId } = parentComment || {};
  const [replies, setReplies] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!postId || !parentId) {
      setLoading(false);
      return;
    }
    const rRef = collection(db, 'posts', postId, 'comments', parentId, 'replies');
    const q = query(rRef, orderBy('createdAt', 'asc'));
    const unsub = onSnapshot(q, async snap => {
      const arr = [];
      for (let ds of snap.docs) {
        const dd = ds.data();
        const up = await fetchUserProfile(dd.uid);
        arr.push({
          id: ds.id,
          ...dd,
          username: up.username,
          userPhoto: up.photoURL,
          profilePic: up.profilePic
        });
      }
      setReplies(arr);
      setLoading(false);
    });
    return () => unsub();
  }, [postId, parentId]);

  if (loading) {
    return <ActivityIndicator color="#fff" style={{ marginTop: 5 }} />;
  }
  if (replies.length === 0) {
    return null;
  }
  if (!expanded) {
    return null;
  }
  return (
    <RepliesList
      postId={postId}
      parentId={parentId}
      replies={replies}
      toggleReplies={toggleReplies}
      timeNow={timeNow}
      handleReplyPress={handleReplyPress}
    />
  );
}

/** RepliesList: Yorum altındaki tüm cevaplar */
function RepliesList({ postId, parentId, replies, toggleReplies, timeNow, handleReplyPress }) {
  const [localReplies, setLocalReplies] = useState(replies);
  const [blockedUsers, setBlockedUsers] = useState([]);
  const navigation = useNavigation();

  // Engellenen kullanıcıları yükle
  useEffect(() => {
    const loadBlockedUsers = async () => {
      if (!auth.currentUser) return;

      try {
        const userRef = doc(db, 'users', auth.currentUser.uid);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          setBlockedUsers(userData.blockedUsers || []);
        }
      } catch (error) {
        console.error('Engellenen kullanıcılar yüklenemedi:', error);
      }
    };

    loadBlockedUsers();
  }, []);

  useEffect(() => {
    // Engellenen kullanıcıların cevaplarını filtrele
    const filteredReplies = replies.filter(reply => !blockedUsers.includes(reply.uid));
    setLocalReplies(filteredReplies);
  }, [replies, blockedUsers]);

  async function toggleReplyLike(replyId) {
    if (!auth.currentUser) return;
    const userId = auth.currentUser.uid;

    // Cevap sahibini bul ve engelleme kontrolü yap
    const reply = localReplies.find(r => r.id === replyId);
    if (!reply) return;

    // Engelleme kontrolü
    if (blockedUsers.includes(reply.uid)) {
      Alert.alert('Uyarı', 'Engellediğiniz kullanıcının cevabını beğenemezsiniz.');
      return;
    }

    // Cevap sahibi bizi engellemiş mi kontrol et
    try {
      const replyOwnerRef = doc(db, 'users', reply.uid);
      const replyOwnerDoc = await getDoc(replyOwnerRef);

      if (replyOwnerDoc.exists()) {
        const replyOwnerData = replyOwnerDoc.data();
        const replyOwnerBlockedUsers = replyOwnerData.blockedUsers || [];

        if (replyOwnerBlockedUsers.includes(userId)) {
          Alert.alert('Uyarı', 'Bu kullanıcı sizi engellediği için cevabını beğenemezsiniz.');
          return;
        }
      }
    } catch (error) {
      console.error('Engelleme kontrolü hatası:', error);
    }
    setLocalReplies(prev =>
      prev.map(r => {
        if (r.id === replyId) {
          const isLiked = r.likedBy?.includes(userId);
          return {
            ...r,
            likes: isLiked ? (r.likes || 0) - 1 : (r.likes || 0) + 1,
            likedBy: isLiked
              ? r.likedBy.filter(x => x !== userId)
              : [...(r.likedBy || []), userId]
          };
        }
        return r;
      })
    );
    try {
      const ref = doc(db, 'posts', postId, 'comments', parentId, 'replies', replyId);
      const snap = await getDoc(ref);
      if (!snap.exists()) return;
      const d = snap.data();
      const isLiked = d.likedBy?.includes(userId);
      await updateDoc(ref, {
        likes: isLiked ? d.likes - 1 : d.likes + 1,
        likedBy: isLiked
          ? d.likedBy.filter(x => x !== userId)
          : [...(d.likedBy || []), userId]
      });
    } catch (err) {
      console.log('toggleReplyLike err:', err);
    }
  }

  function handleShowReplyLikers(reply) {
    if (!reply.likedBy || reply.likedBy.length === 0) {
      Alert.alert('Bilgi', 'Bu cevabı beğenen yok.');
      return;
    }
    navigation.navigate('LikersScreen', { likedBy: reply.likedBy });
  }

  async function handleDeleteReply(item) {
    if (item.uid !== auth.currentUser?.uid) {
      Alert.alert('Hata', 'Sadece kendi cevabınızı silebilirsiniz.');
      return;
    }
    Alert.alert('Sil', 'Bu cevabı silmek istiyor musunuz?', [
      { text: 'İptal', style: 'cancel' },
      {
        text: 'Sil',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteDoc(doc(db, 'posts', postId, 'comments', parentId, 'replies', item.id));
            setLocalReplies(prev => prev.filter(x => x.id !== item.id));
          } catch (e) {
            console.log('deleteReply err:', e);
          }
        }
      }
    ]);
  }

  function goReplyUserProfile(uid) {
    navigation.navigate('OtherProfileScreen', { uid });
  }

  function handleNestedReply(replyItem) {
    handleReplyPress(parentId, replyItem.username, true, replyItem.id);
  }

  if (localReplies.length === 0) return null;

  return (
    <View style={{ marginTop: 8, marginLeft: 35 }}>
      {localReplies.map(r => {
        const isLiked = r.likedBy?.includes(auth.currentUser?.uid);
        const dateText = r.createdAt ? timeAgo(r.createdAt, timeNow) : '';
        const editedSuffix = r.edited ? ' (düzenlendi)' : '';
        let disp = r.reply || '';
        if (disp.length > COMMENT_CHAR_LIMIT) {
          disp = disp.substring(0, COMMENT_CHAR_LIMIT) + '...';
        }
        return (
          <View key={r.id} style={styles.singleReply}>
            <TouchableOpacity onPress={() => goReplyUserProfile(r.uid)}>
              <Image
                source={
                  r.profilePic && avatarMap[r.profilePic]
                    ? avatarMap[r.profilePic]
                    : r.userPhoto
                      ? { uri: r.userPhoto }
                      : require('../assets/default-avatar.png')
                }
                style={styles.replyAvatar}
                defaultSource={require('../assets/default-avatar.png')}
                fadeDuration={0}
                resizeMode="cover"
              />
            </TouchableOpacity>
            <View style={{ flex: 1 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <TouchableOpacity onPress={() => goReplyUserProfile(r.uid)}>
                  <Text style={[styles.username, { fontSize: 15 }]}>{r.username}</Text>
                </TouchableOpacity>
                {r.uid === auth.currentUser?.uid && (
                  <TouchableOpacity onPress={() => handleDeleteReply(r)}>
                    <Ionicons name="trash-outline" size={16} color="#f44" />
                  </TouchableOpacity>
                )}
              </View>
              <View style={{ marginTop: 2 }}>
                {parseAndRenderText(disp)}
              </View>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 4 }}>
                <View style={{ flexDirection: 'row' }}>
                  <TouchableOpacity
                    style={styles.replyActionBtn}
                    onPress={() => toggleReplyLike(r.id)}
                    onLongPress={() => handleShowReplyLikers(r)}
                  >
                    <Ionicons
                      name={isLiked ? 'heart' : 'heart-outline'}
                      size={18}
                      color={isLiked ? '#e74c3c' : '#fff'}
                    />
                    <Text style={styles.replyActionText}>{r.likes || 0}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.replyActionBtn}
                    onPress={() => handleNestedReply(r)}
                  >
                    <Ionicons name="arrow-undo-outline" size={18} color="#fff" />
                    <Text style={styles.replyActionText}>Cevapla</Text>
                  </TouchableOpacity>
                </View>
                <Text style={styles.replyTime}>{dateText + editedSuffix}</Text>
              </View>
            </View>
          </View>
        );
      })}
    </View>
  );
}

/** @mention listesi */
function MentionList({ mentionUsers, onSelect }) {
  return (
    <View style={styles.mentionContainer}>
      <ScrollView>
        {mentionUsers.map(u => (
          <TouchableOpacity
            key={u.id}
            style={styles.mentionItem}
            onPress={() => onSelect(u)}
          >
            <Image
              source={
                u.profilePic && avatarMap[u.profilePic]
                  ? avatarMap[u.profilePic]
                  : u.photoURL
                    ? { uri: u.photoURL }
                    : require('../assets/default-avatar.png')
              }
              style={styles.mentionAvatar}
            />
            <Text style={styles.mentionUsername}>{u.username}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000'
  },
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    borderBottomColor: '#333',
    borderBottomWidth: 0.5,
    backgroundColor: '#000',
    paddingHorizontal: 10
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center'
  },
  emptyListText: {
    color: '#999',
    textAlign: 'center',
    marginTop: 30,
    fontSize: 15
  },
  commentBox: {
    backgroundColor: '#111',
    borderRadius: 8,
    padding: 10,
    marginHorizontal: 10,
    marginVertical: 5
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  commentLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: 19,
    borderWidth: 1,
    borderColor: '#333',
    marginRight: 8
  },
  username: {
    color: '#fff',
    fontSize: 15,
    fontWeight: 'bold'
  },
  editedLabel: {
    color: '#ccc',
    fontSize: 13,
    marginRight: 8,
    fontStyle: 'italic'
  },
  editContainer: {
    backgroundColor: '#222',
    borderRadius: 6,
    padding: 8,
    marginTop: 6
  },
  editInput: {
    backgroundColor: '#333',
    color: '#fff',
    borderRadius: 4,
    fontSize: 15,
    padding: 6,
    minHeight: 40
  },
  editButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8
  },
  cancelBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#444',
    borderRadius: 4,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 8
  },
  cancelBtnText: {
    color: '#fff',
    fontSize: 14
  },
  saveBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0d6efd',
    borderRadius: 4,
    paddingHorizontal: 10,
    paddingVertical: 6
  },
  saveBtnText: {
    color: '#fff',
    fontSize: 14
  },
  commentContent: {
    marginTop: 6
  },
  commentFooter: {
    flexDirection: 'row',
    marginTop: 8,
    alignItems: 'center'
  },
  actionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20
  },
  actionLabel: {
    color: '#fff',
    marginLeft: 5,
    fontSize: 15
  },
  timeText: {
    color: '#aaa',
    fontSize: 13
  },
  toggleRepliesBtn: {
    justifyContent: 'center',
    marginLeft: 10
  },
  toggleRepliesText: {
    color: '#3498db',
    fontSize: 14
  },
  // Reply stilleri
  singleReply: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#1a1a1a',
    borderRadius: 6,
    padding: 8,
    marginBottom: 4
  },
  replyAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#444',
    marginRight: 6
  },
  replyActionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 18
  },
  replyActionText: {
    color: '#fff',
    marginLeft: 4,
    fontSize: 14
  },
  replyTime: {
    color: '#aaa',
    fontSize: 12
  },
  // Alt Input - Klavye ile birlikte hareket eder
  inputBar: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#000',
    borderTopColor: '#333',
    borderTopWidth: 0.5,
    paddingHorizontal: 12,
    paddingTop: 2,
    // position, bottom ve paddingBottom dinamik olarak ayarlanıyor
  },
  input: {
    flex: 1,
    backgroundColor: '#1a1a1a',
    color: '#fff',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    fontSize: 16,
    maxHeight: 120,
    minHeight: 40,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: '#333',
  },
  charCounter: {
    position: 'absolute',
    bottom: 8,
    right: 70,
    fontSize: 12,
    color: '#888',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  sendBtn: {
    backgroundColor: '#0066CC',
    marginLeft: 10,
    padding: 12,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  // Cevap Banner
  replyBanner: {
    backgroundColor: '#1a1a1a',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#333',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  replyBannerText: {
    color: '#0066CC',
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  // @mention listesi
  mentionContainer: {
    position: 'absolute',
    bottom: 70,
    left: 10,
    right: 10,
    maxHeight: 200,
    backgroundColor: '#1a1a1a',
    borderColor: '#0066CC',
    borderWidth: 1,
    borderRadius: 12,
    zIndex: 9999,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8
  },
  mentionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomColor: '#333',
    borderBottomWidth: 0.5,
    backgroundColor: 'transparent'
  },
  mentionAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
    borderWidth: 2,
    borderColor: '#0066CC'
  },
  mentionUsername: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '600'
  }
});
