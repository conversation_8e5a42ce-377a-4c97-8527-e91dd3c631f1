import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Image,
  RefreshControl,
  Modal,
  Pressable,
  Animated,
  ToastAndroid
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useToast } from '../contexts/ToastContext';
import { useUnreadNotifications } from '../utils/useUnreadNotifications';
import PostContent from './PostContent';
import {
  collection,
  query,
  orderBy,
  limit,
  startAfter,
  getDocs,
  doc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  increment,
  addDoc,
  deleteDoc,
  serverTimestamp,
  where,
  getDoc,
  onSnapshot
} from 'firebase/firestore';
import { db, auth } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { createNotification, NOTIFICATION_TYPES } from '../utils/notificationUtils';
import { updateUserPopularity, listenToUserPopularity, listenToPostComments } from '../utils/popularityUtils';

import {
  sendFollowRequest,
  cancelFollowRequest,
  getFollowRequest
} from '../utils/followRequestUtils';

// Avatar mapping: FollowListScreen’de kullandığınız mapping ile uyumlu
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

////////////////////////////////////////////////////////////////////////////////
// Yardımcı: "x süre önce" formatı
////////////////////////////////////////////////////////////////////////////////
function timeAgo(timestamp, currentTime) {
  if (!timestamp || !timestamp.toDate) return 'Bilinmeyen Tarih';
  const postTime = timestamp.toDate().getTime();
  const diff = Math.floor((currentTime - postTime) / 1000);
  if (diff < 0) return '0 saniye önce';
  if (diff < 60) return diff + ' saniye önce';
  if (diff < 3600) return Math.floor(diff / 60) + ' dakika önce';
  if (diff < 86400) return Math.floor(diff / 3600) + ' saat önce';
  return Math.floor(diff / 86400) + ' gün önce';
}

export default function FeedScreen() {
  const currentUser = auth.currentUser;
  const navigation = useNavigation();
  const { isDarkMode } = useTheme();
  const { translations } = useLanguage();
  const { showToast } = useToast();

  // Okunmamış bildirim sayısı
  const unreadNotificationCount = useUnreadNotifications();

  // Güncellenmiş kullanıcı verileri Firestore'dan alınsın
  const [userData, setUserData] = useState(null);
  useEffect(() => {
    if (currentUser) {
      const userRef = doc(db, 'users', currentUser.uid);
      const unsub = onSnapshot(userRef, snap => {
        if (snap.exists()) {
          setUserData(snap.data());
        }
      });
      return () => unsub();
    }
  }, [currentUser]);

  // Gönderiler
  const [posts, setPosts] = useState([]);
  const [lastDoc, setLastDoc] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [commentCounts, setCommentCounts] = useState({});

  // Takip, Engelleme ve Kaydetme
  const [followingIds, setFollowingIds] = useState([]);
  const [blockedUsers, setBlockedUsers] = useState([]);
  const [savedPosts, setSavedPosts] = useState([]);

  // Gerçek zamanlı takip durumu dinleme
  useEffect(() => {
    if (!currentUser) return;

    const userRef = doc(db, 'users', currentUser.uid);
    const unsubscribe = onSnapshot(userRef, (snap) => {
      if (snap.exists()) {
        const userData = snap.data();
        // Takip edilen kullanıcıları güncelle
        setFollowingIds(userData.following || []);
        // Engellenen kullanıcıları güncelle
        setBlockedUsers(userData.blockedUsers || []);
      }
    });

    return () => unsubscribe();
  }, [currentUser]);

  // Beğeni animasyonu
  const likeAnimValues = useRef({});
  const [processingLikes, setProcessingLikes] = useState({});
  const heartOverlayAnim = useRef(new Animated.Value(0)).current;


  // Çift tıklama için
  const lastTapRef = useRef({});
  const doubleTapDelayMs = 300;

  // Çift tıklama ile beğenme fonksiyonu
  const handleDoubleTap = (postId) => {
    // İşlem zaten devam ediyorsa çık
    if (processingLikes[postId]) return;

    // Kullanıcı giriş yapmamışsa çık
    if (!currentUser) return;

    const now = Date.now();
    const lastTap = lastTapRef.current[postId] || 0;

    // Son tıklama zamanını güncelle
    lastTapRef.current[postId] = now;

    // Eğer iki tıklama arasındaki süre belirlenen süreden kısaysa çift tıklama olarak kabul et
    if (now - lastTap < doubleTapDelayMs) {
      // Post'u bul
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      // Beğeni durumunu kontrol et ve beğeni işlemini başlat
      // Çift tıklamada her zaman beğeni işlemini başlat (kalbe basma ile aynı davranış)
      toggleLike(postId);
    }
  };



  // Filtre / Sıralama
  const [selectedTab, setSelectedTab] = useState('forYou');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortOption, setSortOption] = useState('date');

  // Modal state
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [sortModalVisible, setSortModalVisible] = useState(false);
  const [likesModalVisible, setLikesModalVisible] = useState(false);
  const [optionsModalVisible, setOptionsModalVisible] = useState(false);

  // Beğenenler modal
  const [modalLikedUsers, setModalLikedUsers] = useState([]);
  const [modalLoading, setModalLoading] = useState(false);

  // Avatar cache sistemi - performans için
  const avatarCache = useRef({});

  // Avatar source'unu cache'li şekilde al
  const getAvatarSource = (item, isMyPost) => {
    const cacheKey = `${item.uid}-${isMyPost ? userData?.avatarIndex : item.profilePic}`;

    if (avatarCache.current[cacheKey]) {
      return avatarCache.current[cacheKey];
    }

    let source;
    if (isMyPost && userData && userData.avatarIndex !== undefined) {
      source = avatarMap['avatar' + (userData.avatarIndex + 1)];
    } else if (item.profilePic && avatarMap[item.profilePic]) {
      source = avatarMap[item.profilePic];
    } else {
      source = require('../assets/default-avatar.png');
    }

    avatarCache.current[cacheKey] = source;
    return source;
  };

  // Cache'i temizle (memory leak önlemi)
  useEffect(() => {
    return () => {
      avatarCache.current = {};
    };
  }, []);

  // 3 Nokta Menüsü (postId / ownerId)
  const [selectedPostId, setSelectedPostId] = useState(null);
  const [selectedPostOwnerId, setSelectedPostOwnerId] = useState(null);

  // Zaman
  const [timeNow, setTimeNow] = useState(Date.now());

  // Kullanıcı popülerlik değerlerini takip etmek için
  const [, setUserPopularities] = useState({});

  useEffect(() => {
    if (sortOption === 'date') {
      initialLoad();
    }
  }, [sortOption]);

  // Gönderi sahiplerinin popülerlik değerlerini dinle
  useEffect(() => {
    if (posts.length === 0) return;

    const userIds = [...new Set(posts.map(post => post.uid))];
    const unsubscribes = {};

    userIds.forEach(userId => {
      if (userId) {
        unsubscribes[userId] = listenToUserPopularity(userId, (newPopularity) => {
          setUserPopularities(prev => ({
            ...prev,
            [userId]: newPopularity
          }));

          // Gönderi listesini güncelle
          setPosts(prevPosts => {
            const updatedPosts = prevPosts.map(post => {
              if (post.uid === userId) {
                return {
                  ...post,
                  popularity: newPopularity
                };
              }
              return post;
            });
            return updatedPosts;
          });
        });
      }
    });

    // Gönderi yorum sayılarını dinle
    posts.forEach(post => {
      if (post.id) {
        const commentUnsubscribe = listenToPostComments(post.id, (commentCount) => {
          // Gönderi listesini güncelle
          setPosts(prevPosts => {
            return prevPosts.map(p => {
              if (p.id === post.id) {
                return {
                  ...p,
                  commentsCount: commentCount
                };
              }
              return p;
            });
          });
        });

        // Yorum dinleyicisini de ekle
        unsubscribes[`comment_${post.id}`] = commentUnsubscribe;
      }
    });

    return () => {
      Object.values(unsubscribes).forEach(unsub => unsub && unsub());
    };
  }, [posts.length]);

  async function initialLoad() {
    setLoading(true);
    try {
      const postQuery = query(
        collection(db, 'posts'),
        orderBy('createdAt', 'desc'),
        limit(20) // Daha fazla gönderi yükle, engellenenler filtrelendikten sonra yeterli gönderi kalsın
      );
      const snap = await getDocs(postQuery);
      if (!snap.empty) {
        // Tüm gönderileri al
        let docList = snap.docs.map(d => ({
          id: d.id,
          ...d.data(),
          likedBy: d.data().likedBy || [],
          likes: d.data().likes || 0
        }));

        // Engellenen kullanıcıların gönderilerini filtrele
        if (blockedUsers.length > 0) {
          docList = docList.filter(post => !blockedUsers.includes(post.uid));
        }

        // Kullanıcı ID'lerini topla
        const userIds = new Set();
        docList.forEach(post => {
          if (post.uid && post.uid !== currentUser?.uid) {
            userIds.add(post.uid);
          }
        });

        // Kullanıcı verilerini paralel olarak yükle
        const userCache = {};
        await Promise.all(Array.from(userIds).map(async (userId) => {
          try {
            const userDoc = await getDoc(doc(db, 'users', userId));
            if (userDoc.exists()) {
              userCache[userId] = userDoc.data();
            }
          } catch (err) {
            console.error('Kullanıcı verisi yükleme hatası:', err);
          }
        }));

        // Gönderi verilerini kullanıcı bilgileriyle birleştir ve gizli hesap kontrolü yap
        docList = docList.filter(post => {
          if (post.uid && userCache[post.uid]) {
            const userData = userCache[post.uid];

            // Gizli hesap kontrolü: Eğer hesap gizliyse ve takip etmiyorsak gönderiyi gösterme
            if (userData.isPrivateAccount && !userData.followers?.includes(currentUser?.uid)) {
              return false;
            }

            // Gönderi verilerini güncelle
            Object.assign(post, {
              profilePic: userData.profilePic || null,
              username: userData.username || 'Misafir',
              popularity: userData.popularity || 0,
              isPrivateAccount: userData.isPrivateAccount || false
            });
          }
          return true;
        });

        // Yorum sayılarını paralel olarak yükle
        const counts = {};
        await Promise.all(docList.map(async (post) => {
          try {
            const cmRef = collection(db, 'posts', post.id, 'comments');
            const cmSnap = await getDocs(cmRef);
            counts[post.id] = cmSnap.size;
          } catch (err) {
            console.error('Yorum sayısı yükleme hatası:', err);
            counts[post.id] = 0;
          }
        }));

        setPosts(docList);
        const lastVisible = snap.docs[snap.docs.length - 1];
        setLastDoc(lastVisible || null);
        setCommentCounts(counts);

        // Yorum sayılarını canlı olarak dinle
        docList.forEach(post => {
          listenToPostComments(post.id, (commentCount) => {
            setCommentCounts(prev => ({
              ...prev,
              [post.id]: commentCount
            }));
          });
        });
      } else {
        setPosts([]);
        setLastDoc(null);
      }
    } catch (err) {
      console.error('initialLoad hata:', err);
    }
    setLoading(false);
  }

  async function loadMore() {
    if (!lastDoc || sortOption !== 'date') return;
    try {
      const postQuery = query(
        collection(db, 'posts'),
        orderBy('createdAt', 'desc'),
        startAfter(lastDoc),
        limit(20) // Daha fazla gönderi yükle, engellenenler filtrelendikten sonra yeterli gönderi kalsın
      );
      const snap = await getDocs(postQuery);
      if (snap.empty) {
        setLastDoc(null);
        return;
      }

      // Tüm gönderileri al
      let newPosts = snap.docs.map(d => ({
        id: d.id,
        ...d.data(),
        likedBy: d.data().likedBy || [],
        likes: d.data().likes || 0
      }));

      // Engellenen kullanıcıların gönderilerini filtrele
      if (blockedUsers.length > 0) {
        newPosts = newPosts.filter(post => !blockedUsers.includes(post.uid));
      }

      // Kullanıcı ID'lerini topla
      const userIds = new Set();
      newPosts.forEach(post => {
        if (post.uid && post.uid !== currentUser?.uid) {
          userIds.add(post.uid);
        }
      });

      // Kullanıcı verilerini paralel olarak yükle
      const userCache = {};
      await Promise.all(Array.from(userIds).map(async (userId) => {
        try {
          const userDoc = await getDoc(doc(db, 'users', userId));
          if (userDoc.exists()) {
            userCache[userId] = userDoc.data();
          }
        } catch (err) {
          console.error('Kullanıcı verisi yükleme hatası:', err);
        }
      }));

      // Gönderi verilerini kullanıcı bilgileriyle birleştir ve gizli hesap kontrolü yap
      newPosts = newPosts.filter(post => {
        if (post.uid && userCache[post.uid]) {
          const userData = userCache[post.uid];

          // Gizli hesap kontrolü: Eğer hesap gizliyse ve takip etmiyorsak gönderiyi gösterme
          if (userData.isPrivateAccount && !userData.followers?.includes(currentUser?.uid)) {
            return false;
          }

          // Gönderi verilerini güncelle
          Object.assign(post, {
            profilePic: userData.profilePic || null,
            username: userData.username || 'Misafir',
            popularity: userData.popularity || 0,
            isPrivateAccount: userData.isPrivateAccount || false
          });
        }
        return true;
      });

      // Yorum sayılarını paralel olarak yükle
      const counts = { ...commentCounts };
      await Promise.all(newPosts.map(async (post) => {
        try {
          const cmRef = collection(db, 'posts', post.id, 'comments');
          const cmSnap = await getDocs(cmRef);
          counts[post.id] = cmSnap.size;
        } catch (err) {
          console.error('Yorum sayısı yükleme hatası:', err);
          counts[post.id] = 0;
        }
      }));

      setPosts(prev => [...prev, ...newPosts]);
      const lastVisible = snap.docs[snap.docs.length - 1];
      setLastDoc(lastVisible || null);
      setCommentCounts(counts);

      // Yorum sayılarını canlı olarak dinle
      newPosts.forEach(post => {
        listenToPostComments(post.id, (commentCount) => {
          setCommentCounts(prev => ({
            ...prev,
            [post.id]: commentCount
          }));
        });
      });
    } catch (err) {
      console.error('loadMore hata:', err);
    }
  }

  async function onRefresh() {
    setRefreshing(true);
    setTimeNow(Date.now());
    if (sortOption === 'date') {
      await initialLoad();
    } else {
      await loadAllPosts();
    }
    setRefreshing(false);
  }

  async function loadAllPosts() {
    setLoading(true);
    try {
      const postQuery = query(
        collection(db, 'posts'),
        orderBy('createdAt', 'desc')
      );
      const snap = await getDocs(postQuery);
      if (snap.empty) {
        setPosts([]);
        setLastDoc(null);
      } else {
        // Tüm gönderileri al
        let allData = snap.docs.map(d => ({
          id: d.id,
          ...d.data(),
          likedBy: d.data().likedBy || [],
          likes: d.data().likes || 0
        }));

        // Engellenen kullanıcıların gönderilerini filtrele
        if (blockedUsers.length > 0) {
          allData = allData.filter(post => !blockedUsers.includes(post.uid));
        }

        // Kullanıcı ID'lerini topla
        const userIds = new Set();
        allData.forEach(post => {
          if (post.uid && post.uid !== currentUser?.uid) {
            userIds.add(post.uid);
          }
        });

        // Kullanıcı verilerini paralel olarak yükle
        const userCache = {};
        await Promise.all(Array.from(userIds).map(async (userId) => {
          try {
            const userDoc = await getDoc(doc(db, 'users', userId));
            if (userDoc.exists()) {
              userCache[userId] = userDoc.data();
            }
          } catch (err) {
            console.error('Kullanıcı verisi yükleme hatası:', err);
          }
        }));

        // Gönderi verilerini kullanıcı bilgileriyle birleştir ve gizli hesap kontrolü yap
        allData = allData.filter(post => {
          if (post.uid && userCache[post.uid]) {
            const userData = userCache[post.uid];

            // Gizli hesap kontrolü: Eğer hesap gizliyse ve takip etmiyorsak gönderiyi gösterme
            if (userData.isPrivateAccount && !userData.followers?.includes(currentUser?.uid)) {
              return false;
            }

            // Gönderi verilerini güncelle
            Object.assign(post, {
              profilePic: userData.profilePic || null,
              username: userData.username || 'Misafir',
              popularity: userData.popularity || 0,
              isPrivateAccount: userData.isPrivateAccount || false
            });
          }
          return true;
        });

        // Yorum sayılarını paralel olarak yükle
        const counts = {};
        await Promise.all(allData.map(async (post) => {
          try {
            const cmRef = collection(db, 'posts', post.id, 'comments');
            const cmSnap = await getDocs(cmRef);
            counts[post.id] = cmSnap.size;
          } catch (err) {
            console.error('Yorum sayısı yükleme hatası:', err);
            counts[post.id] = 0;
          }
        }));

        setPosts(allData);
        setLastDoc(null);
        setCommentCounts(counts);

        // Yorum sayılarını canlı olarak dinle
        allData.forEach(post => {
          listenToPostComments(post.id, (commentCount) => {
            setCommentCounts(prev => ({
              ...prev,
              [post.id]: commentCount
            }));
          });
        });
      }
    } catch (err) {
      console.error('loadAllPosts hata:', err);
      showToast({
        message: translations.errorLoadingPosts || 'Gönderiler yüklenirken bir hata oluştu.',
        type: 'error'
      });
    }
    setLoading(false);
  }

  useEffect(() => {
    if (currentUser) {
      const userRef = doc(db, 'users', currentUser.uid);
      const unsub = onSnapshot(userRef, snap => {
        if (snap.exists()) {
          const data = snap.data();
          setFollowingIds(data.following || []);
          setBlockedUsers(data.blockedUsers || []);
        }
      });

      return () => unsub();
    }
  }, [currentUser]);

  // Kaydedilen gönderileri ayrı bir useEffect içinde yükle
  useEffect(() => {
    if (currentUser) {
      loadSavedPosts();
    }
  }, [currentUser]);

  // Kaydedilen gönderileri yükle
  const loadSavedPosts = async () => {
    if (!currentUser) return;

    try {
      const savedRef = collection(db, 'savedPosts');
      const q = query(
        savedRef,
        where('userId', '==', currentUser.uid)
      );

      const snapshot = await getDocs(q);
      const savedPostIds = snapshot.docs.map(doc => doc.data().postId);
      setSavedPosts(savedPostIds);
    } catch (error) {
      console.error('Kaydedilen gönderiler yüklenirken hata:', error);
      showToast({
        message: translations.errorLoadingSavedPosts || 'Kaydedilen gönderiler yüklenirken bir hata oluştu.',
        type: 'error'
      });
    }
  };

  async function handleToggleFollow(userId) {
    if (!currentUser) return;

    // Engelleme kontrolü
    if (blockedUsers.includes(userId)) {
      showToast({
        message: 'Engellediğiniz kullanıcıyı takip edemezsiniz.',
        type: 'error'
      });
      return;
    }

    // Önce kullanıcının gizli hesap olup olmadığını kontrol et
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) return;

    const userData = userDoc.data();
    const isPrivateAccount = userData.isPrivateAccount || false;

    // Hedef kullanıcının bizi engellemiş olup olmadığını kontrol et
    const targetUserBlockedUsers = userData.blockedUsers || [];
    if (targetUserBlockedUsers.includes(currentUser.uid)) {
      showToast({
        message: 'Bu kullanıcı sizi engellediği için takip edemezsiniz.',
        type: 'error'
      });
      return;
    }

    // Gerçek takip durumunu sunucudan kontrol et (senkronizasyon için)
    const actuallyFollowing = userData.followers?.includes(currentUser.uid) || false;
    const isFollowing = followingIds.includes(userId);

    // Eğer local state ile sunucu durumu farklıysa, local state'i düzelt
    if (isFollowing !== actuallyFollowing) {
      if (actuallyFollowing) {
        setFollowingIds(prev => [...prev, userId]);
      } else {
        setFollowingIds(prev => prev.filter(id => id !== userId));
      }
    }

    // Gizli hesap kontrolü (güncellenmiş takip durumu ile)
    if (isPrivateAccount && !actuallyFollowing) {
      // Takip isteği gönder/iptal et
      await handleFollowRequestFromFeed(userId);
      return;
    }

    if (actuallyFollowing) {
      // Takipten çık - UI güncelleme
      setFollowingIds(prev => prev.filter(x => x !== userId));
    } else {
      // Takip et - UI güncelleme
      setFollowingIds(prev => [...prev, userId]);
    }

    // Arka planda Firestore işlemlerini yap
    const updateFollowStatus = async () => {
      try {
        if (actuallyFollowing) {
          // Takipten çık - Firestore işlemleri
          const qF = query(
            collection(db, 'follows'),
            where('followerUid', '==', currentUser.uid),
            where('followingUid', '==', userId)
          );

          getDocs(qF).then(snap => {
            snap.forEach(s => {
              deleteDoc(s.ref).catch(err => console.error('Follow dokümanı silme hatası:', err));
            });
          }).catch(err => console.error('Follow dokümanı bulma hatası:', err));

          updateDoc(doc(db, 'users', userId), {
            followers: arrayRemove(currentUser.uid)
          }).catch(err => console.error('Takipçi kaldırma hatası:', err));

          updateDoc(doc(db, 'users', currentUser.uid), {
            following: arrayRemove(userId)
          }).catch(err => console.error('Takip kaldırma hatası:', err));

          // Popülerlik değerini güncelle
          updateUserPopularity(userId, true).catch(err => console.error('Popülerlik güncelleme hatası:', err));
        } else {
          // Takip et - Firestore işlemleri
          addDoc(collection(db, 'follows'), {
            followerUid: currentUser.uid,
            followingUid: userId,
            createdAt: serverTimestamp()
          }).catch(err => console.error('Follow dokümanı ekleme hatası:', err));

          updateDoc(doc(db, 'users', userId), {
            followers: arrayUnion(currentUser.uid)
          }).catch(err => console.error('Takipçi ekleme hatası:', err));

          updateDoc(doc(db, 'users', currentUser.uid), {
            following: arrayUnion(userId)
          }).catch(err => console.error('Takip ekleme hatası:', err));

          // Takip bildirimi oluştur
          createNotification(
            NOTIFICATION_TYPES.FOLLOW,
            currentUser.uid,
            userId
          ).catch(err => console.error('Bildirim oluşturma hatası:', err));

          // Popülerlik değerini güncelle
          updateUserPopularity(userId, true).catch(err => console.error('Popülerlik güncelleme hatası:', err));
        }
      } catch (err) {
        console.error('Takip toggle error:', err);

        // Hata durumunda UI'ı geri al
        if (actuallyFollowing) {
          setFollowingIds(prev => [...prev, userId]);
        } else {
          setFollowingIds(prev => prev.filter(x => x !== userId));
        }

        showToast({
          message: translations.followError || 'Takip işlemi sırasında bir hata oluştu.',
          type: 'error'
        });
      }
    };

    // Arka planda işlemi başlat
    updateFollowStatus();
  }

  // Takip isteği gönder/iptal et (FeedScreen için)
  async function handleFollowRequestFromFeed(userId) {
    if (!currentUser) return;

    // Engelleme kontrolü
    if (blockedUsers.includes(userId)) {
      showToast({
        message: 'Engellediğiniz kullanıcıya takip isteği gönderemezsiniz.',
        type: 'error'
      });
      return;
    }

    // Hedef kullanıcının bizi engellemiş olup olmadığını kontrol et
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const targetUserBlockedUsers = userData.blockedUsers || [];
      if (targetUserBlockedUsers.includes(currentUser.uid)) {
        showToast({
          message: 'Bu kullanıcı sizi engellediği için takip isteği gönderemezsiniz.',
          type: 'error'
        });
        return;
      }
    }

    try {
      // Mevcut takip isteği var mı kontrol et
      const existingRequest = await getFollowRequest(currentUser.uid, userId);

      if (existingRequest) {
        // İsteği iptal et
        const success = await cancelFollowRequest(currentUser.uid, userId);
        if (success) {
          showToast({
            message: 'Takip isteği iptal edildi',
            type: 'success'
          });
        } else {
          showToast({
            message: 'İstek iptal edilirken hata oluştu',
            type: 'error'
          });
        }
      } else {
        // İstek gönder
        const success = await sendFollowRequest(currentUser.uid, userId);
        if (success) {
          showToast({
            message: 'Takip isteği gönderildi',
            type: 'success'
          });
        } else {
          showToast({
            message: 'İstek gönderilirken hata oluştu',
            type: 'error'
          });
        }
      }
    } catch (error) {
      console.error('Takip isteği işlemi hatası:', error);
      showToast({
        message: 'Bir hata oluştu',
        type: 'error'
      });
    }
  }

  // Beğeni işlemi

  function toggleLike(postId) {
    if (!currentUser) return;

    // Beğeni işlemi devam ediyorsa çık
    if (processingLikes[postId]) return;

    // İşlem başladı - Hemen işlemi kilitle
    setProcessingLikes(prev => ({ ...prev, [postId]: true }));

    // Beğeni animasyonu için Animated.Value oluştur
    if (!likeAnimValues.current[postId]) {
      likeAnimValues.current[postId] = new Animated.Value(1);
    }

    // Önce mevcut durumu kontrol et
    const post = posts.find(p => p.id === postId);
    if (!post) {
      setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      return;
    }

    // Engelleme kontrolü
    if (blockedUsers.includes(post.uid)) {
      setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      showToast({
        message: 'Engellediğiniz kullanıcının gönderisini beğenemezsiniz.',
        type: 'error'
      });
      return;
    }

    // Beğeni durumu sunucudan kontrol edilecek

    // Arka planda Firestore işlemlerini yap
    const postRef = doc(db, 'posts', postId);

    getDoc(postRef).then(async snap => {
      if (!snap.exists()) {
        // Post bulunamadı
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        return;
      }

      const data = snap.data();

      // Gönderi sahibi bizi engellemiş mi kontrol et
      try {
        const postOwnerRef = doc(db, 'users', data.uid);
        const postOwnerDoc = await getDoc(postOwnerRef);

        if (postOwnerDoc.exists()) {
          const postOwnerData = postOwnerDoc.data();
          const postOwnerBlockedUsers = postOwnerData.blockedUsers || [];

          if (postOwnerBlockedUsers.includes(currentUser.uid)) {
            setProcessingLikes(prev => ({ ...prev, [postId]: false }));
            showToast({
              message: 'Bu kullanıcı sizi engellediği için gönderisini beğenemezsiniz.',
              type: 'error'
            });
            return;
          }
        }
      } catch (error) {
        console.error('Engelleme kontrolü hatası:', error);
      }
      const serverIsLiked = data.likedBy?.includes(currentUser.uid);

      // Sunucu durumu ile istemci durumu farklıysa, sunucu durumunu kullan
      // Bu, hızlı ardışık beğeni işlemlerinde tutarlılık sağlar
      const finalIsLiked = serverIsLiked;

      // Beğeni sayısı 0'dan küçük olamaz
      const newLikes = finalIsLiked ? Math.max(0, data.likes - 1) : (data.likes || 0) + 1;

      // Animasyonu başlat (UI güncellemesinden önce)
      if (finalIsLiked) {
        // Beğeni kaldırma animasyonu (hafif küçültme)
        Animated.sequence([
          Animated.spring(likeAnimValues.current[postId], {
            toValue: 0.8,
            friction: 3,
            tension: 100,
            useNativeDriver: true
          }),
          Animated.spring(likeAnimValues.current[postId], {
            toValue: 1,
            friction: 3,
            tension: 80,
            useNativeDriver: true
          })
        ]).start();
      } else {
        // Beğeni ekleme animasyonu (büyütme)
        Animated.sequence([
          Animated.spring(likeAnimValues.current[postId], {
            toValue: 1.3,
            friction: 2,
            tension: 120,
            useNativeDriver: true
          }),
          Animated.spring(likeAnimValues.current[postId], {
            toValue: 1,
            friction: 3,
            tension: 80,
            useNativeDriver: true
          })
        ]).start();
      }

      // UI'ı optimize edilmiş şekilde güncelle - sadece değişen post'u güncelle
      setPosts(prev => {
        const postIndex = prev.findIndex(p => p.id === postId);
        if (postIndex === -1) return prev;

        const updatedPost = { ...prev[postIndex] };
        updatedPost.likes = newLikes;

        if (finalIsLiked) {
          updatedPost.likedBy = updatedPost.likedBy?.filter(uid => uid !== currentUser.uid) || [];
        } else {
          updatedPost.likedBy = [...(updatedPost.likedBy || []), currentUser.uid];
        }

        // Yeni array oluştur ama sadece değişen elementi değiştir
        const newPosts = [...prev];
        newPosts[postIndex] = updatedPost;
        return newPosts;
      });

      // Firestore güncelleme
      updateDoc(postRef, {
        likes: newLikes, // increment yerine doğrudan değer atama
        likedBy: finalIsLiked
          ? arrayRemove(currentUser.uid)
          : arrayUnion(currentUser.uid),
        lastLikeUpdate: serverTimestamp()
      }).then(() => {
        // Beğeni bildirimi oluştur
        if (!finalIsLiked && data.uid !== currentUser.uid) {
          createNotification(
            NOTIFICATION_TYPES.LIKE,
            currentUser.uid,
            data.uid,
            postId
          );


        }

        // Popülerlik değerini güncelle
        if (data.uid) {
          // Zorla güncelleme yap (true) ve sonucu bekle
          return updateUserPopularity(data.uid, true);
        }
      }).catch(() => {
        // Beğeni güncelleme hatası
      }).finally(() => {
        // İşlem bitti - 500ms sonra kilidi kaldır (hızlı ardışık tıklamaları engelle)
        setTimeout(() => {
          setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        }, 500);
      });
    }).catch(() => {
      // toggleLike hata
      // İşlem bitti - 500ms sonra kilidi kaldır
      setTimeout(() => {
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      }, 500);
    });
  }

  function handleCommentPress(postId) {
    navigation.navigate('Comments', { postId });
  }

  function openOptionsModal(postId, ownerId) {
    setSelectedPostId(postId);
    setSelectedPostOwnerId(ownerId);
    setOptionsModalVisible(true);
  }

  // Gönderi kaydetme işlevi
  function handleSavePost(postId) {
    if (!currentUser || !postId) return;

    // Önce UI'ı güncelle (anında tepki için)
    const isSaved = savedPosts.includes(postId);

    if (isSaved) {
      // Gönderiyi kaydedenler listesinden çıkar - UI güncelleme
      setSavedPosts(prev => prev.filter(id => id !== postId));
      showToast({
        message: translations.postRemovedFromSaved || 'Gönderi kayıtlardan kaldırıldı.',
        type: 'success'
      });
    } else {
      // Gönderiyi kaydet - UI güncelleme
      setSavedPosts(prev => [...prev, postId]);
      showToast({
        message: translations.postSaved || 'Gönderi kaydedildi.',
        type: 'success'
      });
    }

    // Arka planda Firestore işlemlerini yap
    const updateSaveStatus = async () => {
      try {
        if (isSaved) {
          // Gönderiyi kaydedenler listesinden çıkar - Firestore işlemi
          const savedRef = collection(db, 'savedPosts');
          const q = query(
            savedRef,
            where('userId', '==', currentUser.uid),
            where('postId', '==', postId)
          );

          getDocs(q).then(snapshot => {
            if (!snapshot.empty) {
              deleteDoc(snapshot.docs[0].ref).catch(err =>
                console.error('Kayıt silme hatası:', err)
              );
            }
          }).catch(err => console.error('Kayıt bulma hatası:', err));
        } else {
          // Gönderiyi kaydet - Firestore işlemi
          addDoc(collection(db, 'savedPosts'), {
            userId: currentUser.uid,
            postId: postId,
            savedAt: serverTimestamp()
          }).catch(err => console.error('Kaydetme hatası:', err));
        }
      } catch (error) {
        console.error('Gönderi kaydetme hatası:', error);

        // Hata durumunda UI'ı geri al
        if (isSaved) {
          setSavedPosts(prev => [...prev, postId]);
          showToast({
            message: 'Gönderi kayıtlardan kaldırılırken bir hata oluştu.',
            type: 'error'
          });
        } else {
          setSavedPosts(prev => prev.filter(id => id !== postId));
          showToast({
            message: translations.errorSavingPost || 'Gönderi kaydedilirken bir hata oluştu.',
            type: 'error'
          });
        }
      }
    };

    // Arka planda işlemi başlat
    updateSaveStatus();
  }
  function closeOptionsModal() {
    setOptionsModalVisible(false);
    setSelectedPostId(null);
    setSelectedPostOwnerId(null);
  }
  async function deletePost(postId) {
    try {
      const cRef = collection(db, 'posts', postId, 'comments');
      const cSnap = await getDocs(cRef);
      for (let c of cSnap.docs) {
        await deleteDoc(c.ref);
      }
      await deleteDoc(doc(db, 'posts', postId));
      setPosts(prev => prev.filter(x => x.id !== postId));
    } catch (err) {
      console.error('deletePost hata:', err);
      showToast({
        message: translations.errorDeletingPost || 'Gönderi silinirken bir hata oluştu.',
        type: 'error'
      });
    }
  }
  function handleOption(opt) {
    if (opt === 'delete' && selectedPostId) {
      deletePost(selectedPostId);
    } else if (opt === 'report') {
      showToast({
        message: translations.postReported || 'Gönderi bildirildi. İnceleme sonrası gerekli işlemler yapılacaktır.',
        type: 'success'
      });
    } else if (opt === 'block' && selectedPostOwnerId) {
      blockUser(selectedPostOwnerId);
    }
    closeOptionsModal();
  }

  // Kullanıcı engelleme işlevi
  function blockUser(userId) {
    if (!currentUser || !userId || userId === currentUser.uid) return;

    // Kullanıcı kendi kendini engelleyemez
    if (userId === currentUser.uid) {
      ToastAndroid.show('Kendinizi engelleyemezsiniz.', ToastAndroid.SHORT);
      return;
    }

    // Önce UI'ı güncelle (anında tepki için)
    setBlockedUsers(prev => [...prev, userId]);

    // Engellenen kullanıcının gönderilerini filtrele
    setPosts(prev => prev.filter(post => post.uid !== userId));

    ToastAndroid.show('Kullanıcı engellendi.', ToastAndroid.SHORT);

    // Arka planda Firestore işlemlerini yap
    const updateBlockStatus = async () => {
      try {
        const userRef = doc(db, 'users', currentUser.uid);
        const targetUserRef = doc(db, 'users', userId);
        const userDoc = await getDoc(userRef);

        if (!userDoc.exists()) {
          console.error('Kullanıcı bilgileri bulunamadı.');
          return;
        }

        const userData = userDoc.data();
        const blockedList = userData.blockedUsers || [];

        // Kullanıcı zaten engellenmişse
        if (blockedList.includes(userId)) {
          console.log('Bu kullanıcı zaten engellenmiş.');
          return;
        }

        // ÇİFT YÖNLÜ ENGELLEME - Her iki kullanıcı da birbirini engellesin
        await updateDoc(userRef, {
          blockedUsers: arrayUnion(userId)
        });
        await updateDoc(targetUserRef, {
          blockedUsers: arrayUnion(currentUser.uid)
        });

        // Takip ilişkilerini kaldır
        // 1. Eğer ben onu takip ediyorsam, takipten çık
        if (followingIds.includes(userId)) {
          await updateDoc(userRef, {
            following: arrayRemove(userId)
          });
          await updateDoc(targetUserRef, {
            followers: arrayRemove(currentUser.uid)
          });

          // follows koleksiyonundan da sil
          const followQuery = query(
            collection(db, 'follows'),
            where('followerUid', '==', currentUser.uid),
            where('followingUid', '==', userId)
          );
          const followDocs = await getDocs(followQuery);
          followDocs.forEach(async (doc) => {
            await deleteDoc(doc.ref);
          });

          // UI'ı güncelle
          setFollowingIds(prev => prev.filter(id => id !== userId));
        }

        // 2. Eğer o beni takip ediyorsa, onu takipçilerimden çıkar
        const myFollowers = userData.followers || [];
        if (myFollowers.includes(userId)) {
          await updateDoc(userRef, {
            followers: arrayRemove(userId)
          });
          await updateDoc(targetUserRef, {
            following: arrayRemove(currentUser.uid)
          });

          // follows koleksiyonundan da sil
          const reverseFollowQuery = query(
            collection(db, 'follows'),
            where('followerUid', '==', userId),
            where('followingUid', '==', currentUser.uid)
          );
          const reverseFollowDocs = await getDocs(reverseFollowQuery);
          reverseFollowDocs.forEach(async (doc) => {
            await deleteDoc(doc.ref);
          });
        }
      } catch (error) {
        console.error('Kullanıcı engelleme hatası:', error);

        // Hata durumunda UI'ı geri al
        setBlockedUsers(prev => prev.filter(id => id !== userId));

        // Gönderileri geri yükle
        initialLoad();

        ToastAndroid.show('Kullanıcı engellenirken bir hata oluştu.', ToastAndroid.SHORT);
      }
    };

    // Arka planda işlemi başlat
    updateBlockStatus();
  }

  // Filtre ve sıralama modallarını açma/kapama fonksiyonları
  const closeFilterModal = () => {
    setFilterModalVisible(false);
  };

  const closeSortModal = () => {
    setSortModalVisible(false);
  };
  async function handleSortSelect(opt) {
    setSortOption(opt);
    closeSortModal();
    if (opt === 'likes' || opt === 'comments') {
      await loadAllPosts();
    } else if (opt === 'date') {
      await initialLoad();
    }
  }
  function handleFilterSelect(cat) {
    setSelectedCategory(cat);
    closeFilterModal();
  }
  function handleEndReached() {
    if (!loading && sortOption === 'date' && lastDoc) {
      loadMore();
    }
  }

  const filteredPosts = posts.filter(p => {
    // Engellenen kullanıcıların gönderilerini filtrele
    if (blockedUsers.includes(p.uid)) return false;

    const tabOk = selectedTab === 'following'
      ? followingIds.includes(p.uid)
      : true;
    let catOk = true;
    if (selectedCategory === 'song') catOk = p.type === 'Şarkı Sözü';
    else if (selectedCategory === 'poem') catOk = p.type === 'Şiir';
    return tabOk && catOk;
  });

  // Duplicate'ları temizle
  const uniquePosts = filteredPosts.filter((post, index, self) =>
    index === self.findIndex(p => p.id === post.id)
  );

  let finalPosts = [...uniquePosts];
  if (sortOption === 'likes') {
    finalPosts.sort((a, b) => b.likes - a.likes);
  } else if (sortOption === 'comments') {
    finalPosts.sort((a, b) => (commentCounts[b.id] || 0) - (commentCounts[a.id] || 0));
  } else if (sortOption === 'date') {
    finalPosts.sort((a, b) => b.createdAt.toDate() - a.createdAt.toDate());
  }

  async function handleShowLikes(likedByArray) {
    if (!likedByArray || likedByArray.length === 0) return;
    setModalLoading(true);
    try {
      const userPromises = likedByArray.map(async uid => {
        const userRef = doc(db, 'users', uid);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          return { id: uid, ...userSnap.data() };
        }
        return null;
      });
      const results = await Promise.all(userPromises);
      const validUsers = results.filter(u => u);

      // Engellenen kullanıcıları filtrele
      const filteredUsers = validUsers.filter(user => !blockedUsers.includes(user.id));

      setModalLikedUsers(filteredUsers);
      setLikesModalVisible(true);
    } catch (err) {
      console.error('handleShowLikes hata:', err);
    }
    setModalLoading(false);
  }

  function renderOptionsModal() {
    if (!optionsModalVisible) return null;
    const isMine = selectedPostOwnerId === currentUser?.uid;
    return (
      <Modal
        visible={optionsModalVisible}
        transparent
        animationType="fade"
        onRequestClose={closeOptionsModal}
      >
        <Pressable style={styles.optionsModalOverlay} onPress={closeOptionsModal}>
          <>
            <View style={styles.optionsModalContainer}>
              <Text style={styles.optionsModalTitle}>Seçenekler</Text>
              {isMine ? (
                <TouchableOpacity
                  style={styles.optionsModalOption}
                  onPress={() => handleOption('delete')}
                >
                  <Ionicons name="trash-outline" size={22} color="#FF4444" />
                  <Text style={[styles.optionsModalOptionText, { color: '#FF4444' }]}>
                    Gönderiyi Sil
                  </Text>
                </TouchableOpacity>
              ) : (
                <>
                  <TouchableOpacity
                    style={styles.optionsModalOption}
                    onPress={() => handleOption('report')}
                  >
                    <Ionicons name="alert-circle-outline" size={22} color="#FF8888" />
                    <Text style={styles.optionsModalOptionText}>Bildir</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.optionsModalOption}
                    onPress={() => handleOption('block')}
                  >
                    <Ionicons name="hand-left-outline" size={22} color="#FF8888" />
                    <Text style={styles.optionsModalOptionText}>Engelle</Text>
                  </TouchableOpacity>
                </>
              )}
              <TouchableOpacity
                style={[styles.optionsModalOption, { justifyContent: 'center' }]}
                onPress={closeOptionsModal}
              >
                <Text style={[styles.optionsModalOptionText, { color: '#aaa' }]}>
                  Kapat
                </Text>
              </TouchableOpacity>
            </View>
          </>
        </Pressable>
      </Modal>
    );
  }
  function renderFilterModal() {
    return (
      <Modal
        visible={filterModalVisible}
        transparent
        animationType="fade"
        onRequestClose={closeFilterModal}
      >
        <Pressable style={styles.modalOverlay} onPress={closeFilterModal}>
          <View style={styles.filterContainer}>
            <Text style={styles.modalTitle}>KATEGORİ FİLTRESİ</Text>
            {renderFilterOption('all', 'Tüm Gönderiler')}
            {renderFilterOption('song', 'Şarkılar')}
            {renderFilterOption('poem', 'Şiirler')}
          </View>
        </Pressable>
      </Modal>
    );
  }
  function renderFilterOption(cat, label) {
    const active = selectedCategory === cat;
    return (
      <TouchableOpacity style={styles.filterOption} onPress={() => handleFilterSelect(cat)}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={{ color: '#fff', fontSize: 16 }}>{label}</Text>
          {active && (
            <Ionicons name="checkmark-circle" size={18} color="#1abc9c" style={{ marginLeft: 8 }} />
          )}
        </View>
      </TouchableOpacity>
    );
  }
  function renderSortModal() {
    return (
      <Modal
        visible={sortModalVisible}
        transparent
        animationType="fade"
        onRequestClose={closeSortModal}
      >
        <Pressable style={styles.modalOverlay} onPress={closeSortModal}>
          <View style={styles.filterContainer}>
            <Text style={styles.modalTitle}>SIRALAMA</Text>
            {renderSortOption('likes', 'Beğenmeye Göre')}
            {renderSortOption('comments', 'Yoruma Göre')}
            {renderSortOption('date', 'Yüklenme Tarihine Göre')}
          </View>
        </Pressable>
      </Modal>
    );
  }
  function renderSortOption(opt, label) {
    const active = sortOption === opt;
    return (
      <TouchableOpacity style={styles.filterOption} onPress={() => handleSortSelect(opt)}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={{ color: '#fff', fontSize: 16 }}>{label}</Text>
          {active && (
            <Ionicons name="checkmark-circle" size={18} color="#1abc9c" style={{ marginLeft: 8 }} />
          )}
        </View>
      </TouchableOpacity>
    );
  }
  // ───────────────────────────────────────────────────────────────
  // Üst BAR (Tek bir renderHeader kullanın): Sol tarafta tıklanabilir avatar (önceden OtherProfile'ye gidiyordu; şimdi 'Profil'e), ortada "VELMORA", sağ tarafta ise sıralama butonu solda ve filtre butonu sağda; ayrıca en sağda bildirim (zil) ikonu
  function renderHeader() {
    return (
      <View style={[styles.topHeader, { backgroundColor: '#000000' }]}>
        <TouchableOpacity
          onPress={() => {
            // Performans iyileştirmesi için navigasyon öncesi hazırlık
            requestAnimationFrame(() => {
              navigation.navigate('Profil');
            });
          }}
          style={styles.headerLeft}
          activeOpacity={0.7}
        >
          <Image
            source={
              userData && userData.profilePic && avatarMap[userData.profilePic]
                ? avatarMap[userData.profilePic]
                : userData && userData.avatarIndex !== undefined
                  ? avatarMap['avatar' + (userData.avatarIndex + 1)]
                  : require('../assets/default-avatar.png')
            }
            style={styles.topAvatar}
          />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.topTitle}>VELMORA</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            onPress={() => navigation.navigate('Notifications')}
            style={styles.notificationButton}
          >
            <Ionicons
              name="notifications-outline"
              size={26}
              color={unreadNotificationCount > 0 ? "#FF4444" : "#fff"}
            />
            {unreadNotificationCount > 0 && (
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationBadgeText}>
                  {unreadNotificationCount > 99 ? '99+' : unreadNotificationCount}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  // ───────────────────────────────────────────────────────────────
  // Gönderi Kartı: Avatar ve isim tıklandığında; eğer gönderi sahibi mevcut kullanıcıysa 'Profil', değilse OtherProfile’ye yönlensin.




  const renderPostItem = ({ item }) => {
    const displayName = (item.username?.trim() !== '') ? item.username : 'Misafir';
    const isLiked = (item.likedBy || []).includes(currentUser?.uid ?? '');
    const commentCount = commentCounts[item.id] || 0;
    const isFollowing = followingIds.includes(item.uid);
    const isMyPost = item.uid === currentUser?.uid;

    // Animasyon değerini sadece bir kez oluştur
    if (!likeAnimValues.current[item.id]) {
      likeAnimValues.current[item.id] = new Animated.Value(1);
    }

    // Avatar source'unu cache'den al
    const avatarSource = getAvatarSource(item, isMyPost);

    // Kullanıcı ünvanını belirle
    const userRank = item.popularity >= 1000 ? 'Efsane' :
      item.popularity >= 500 ? 'Yıldız' :
      item.popularity >= 200 ? 'Popüler' :
      item.popularity >= 100 ? 'Yükselen' :
      item.popularity >= 50 ? 'Aktif' :
      item.popularity >= 20 ? 'Başlangıç' :
      item.popularity > 0 ? 'Yeni Üye' : '';

    return (
      <View style={styles.card}>
        <View style={styles.postHeader}>
          <View style={styles.headerLeft}>
            <TouchableOpacity
              onPress={() => {
                // Performans iyileştirmesi için navigasyon öncesi hazırlık
                requestAnimationFrame(() => {
                  if (item.uid === currentUser?.uid) {
                    navigation.navigate('MainTabs', { screen: 'Profil' });
                  } else {
                    navigation.navigate('OtherProfile', { uid: item.uid });
                  }
                });
              }}
              activeOpacity={0.7}
            >
              <Image
                source={avatarSource}
                style={styles.avatar}
                resizeMode="cover"
                onError={() => {
                  // Avatar yükleme hatası durumunda default avatar'a geç
                  console.log('Avatar yükleme hatası, default avatar kullanılıyor');
                }}
              />
            </TouchableOpacity>
            <View style={styles.userInfoColumn}>
              <View style={styles.userRow}>
                <TouchableOpacity
                  onPress={() => {
                    // Performans iyileştirmesi için navigasyon öncesi hazırlık
                    requestAnimationFrame(() => {
                      if (item.uid === currentUser?.uid) {
                        navigation.navigate('MainTabs', { screen: 'Profil' });
                      } else {
                        navigation.navigate('OtherProfile', { uid: item.uid });
                      }
                    });
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.username}>{displayName}</Text>
                  <Text style={styles.userRank}>{userRank || 'Yeni Üye'}</Text>
                </TouchableOpacity>
                {!isMyPost && (
                  <TouchableOpacity
                    style={[styles.followButton, { marginLeft: 8 }]}
                    onPress={() => handleToggleFollow(item.uid)}
                  >
                    <Text style={styles.followButtonText}>
                      {isFollowing ? 'Takipten Çık' : 'Takip Et'}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>
          <View style={styles.headerRight}>
            <Text style={styles.postTypeLabel}>
              {item.type === 'Şarkı Sözü'
                ? 'Şarkı'
                : item.type === 'Şiir'
                  ? 'Şiir'
                  : item.type}
            </Text>
            <TouchableOpacity
              style={styles.optionsButton}
              onPress={() => openOptionsModal(item.id, item.uid)}
            >
              <Ionicons name="ellipsis-horizontal" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
        <TouchableOpacity
          style={styles.cardContent}
          activeOpacity={0.9}
          onPress={() => handleDoubleTap(item.id)}
        >
          <PostContent content={item.content} maxLength={150} style={styles.content} />


        </TouchableOpacity>
        <View style={styles.cardFooter}>
          <View style={styles.footerLeft}>
            <TouchableOpacity
              style={styles.likeContainer}
              onPress={() => toggleLike(item.id)}
              onLongPress={() => handleShowLikes(item.likedBy)}
              disabled={processingLikes[item.id]}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Animated.View style={{ transform: [{ scale: likeAnimValues.current[item.id] }] }}>
                <Ionicons
                  name={isLiked ? 'heart' : 'heart-outline'}
                  size={24}
                  color={isLiked ? '#e74c3c' : '#fff'}
                />
              </Animated.View>
              <Text style={[styles.actionText, { marginLeft: 6 }]}>{item.likes}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { marginLeft: -2 }]}
              onPress={() => handleCommentPress(item.id)}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Ionicons name="chatbubble-outline" size={24} color="#fff" />
              <Text style={styles.actionText}>Yorum ({commentCount})</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, { marginLeft: 10 }]}
              onPress={() => handleSavePost(item.id)}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Ionicons
                name={savedPosts.includes(item.id) ? "bookmark" : "bookmark-outline"}
                size={22}
                color="#fff"
              />
              <Text style={styles.actionText}>{savedPosts.includes(item.id) ? "Kaydedildi" : "Kaydet"}</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.timestampFooter}>
            {timeAgo(item.createdAt, timeNow)}
          </Text>
        </View>
      </View>
    );
  };
  // ───────────────────────────────────────────────────────────────
  // Beğeni Modalı: Her satırdaki avatar veya isim tıklandığında; eğer beğenen mevcut kullanıcıysa 'Profil', değilse OtherProfile’ye yönlensin.
  function renderLikesModal() {
    return (
      <Modal
        visible={likesModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setLikesModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Beğenenler</Text>
            {modalLoading ? (
              <ActivityIndicator size="large" color="#8e44ad" />
            ) : (
              <FlatList
                data={modalLikedUsers}
                keyExtractor={u => u.id}
                renderItem={({ item }) => (
                  <View style={styles.modalUserRow}>
                    <TouchableOpacity
                      onPress={() => {
                        setLikesModalVisible(false);
                        if (item.id === currentUser?.uid) {
                          navigation.navigate('Hesap');
                        } else {
                          navigation.navigate('OtherProfile', { uid: item.id });
                        }
                      }}
                    >
                      <Image
                        source={
                          item.profilePic && avatarMap[item.profilePic]
                            ? avatarMap[item.profilePic]
                            : require('../assets/default-avatar.png')
                        }
                        style={styles.modalAvatar}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setLikesModalVisible(false);
                        if (item.id === currentUser?.uid) {
                          navigation.navigate('Hesap');
                        } else {
                          navigation.navigate('OtherProfile', { uid: item.id });
                        }
                      }}
                      style={{ flex: 1 }}
                    >
                      <Text style={styles.modalUsername}>{item.username || 'Kullanıcı'}</Text>
                    </TouchableOpacity>
                    {item.id !== currentUser?.uid && (
                      <TouchableOpacity
                        onPress={() => handleToggleFollow(item.id)}
                        style={styles.followButtonModal}
                      >
                        <Text style={styles.followButtonModalText}>
                          {followingIds.includes(item.id) ? 'Takipten Çık' : 'Takip Et'}
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}
                ListEmptyComponent={<Text style={styles.noLikesText}>Henüz beğenen yok.</Text>}
              />
            )}
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setLikesModalVisible(false)}
            >
              <Text style={styles.modalCloseButtonText}>Kapat</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }
  // ───────────────────────────────────────────────────────────────
  // RenderOptionsModal: Removed duplicate function
  // This function is already defined earlier in the code

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#000' : '#fff' }]}>
      {renderHeader()}
      <View style={[styles.tabBar, { backgroundColor: '#000000' }]}>
        <TouchableOpacity onPress={() => setSortModalVisible(true)}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons name="arrow-up" size={20} color="#fff" style={{ marginRight: 2 }} />
            <Ionicons name="arrow-down" size={20} color="#fff" />
          </View>
        </TouchableOpacity>
        <View style={{ flexDirection: 'row', justifyContent: 'center', flex: 1 }}>
          <TouchableOpacity
            style={[styles.tabItem, selectedTab === 'forYou' && styles.activeTab]}
            onPress={() => setSelectedTab('forYou')}
          >
            <Text style={[styles.tabText, selectedTab === 'forYou' && styles.activeTabText]}>
              {translations.forYou}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tabItem, selectedTab === 'following' && styles.activeTab]}
            onPress={() => setSelectedTab('following')}
          >
            <Text style={[styles.tabText, selectedTab === 'following' && styles.activeTabText]}>
              {translations.following}
            </Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={() => setFilterModalVisible(true)}>
          <Ionicons name="funnel-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#8e44ad" />
        </View>
      ) : (
        <FlatList
          data={finalPosts}
          keyExtractor={item => item.id}
          renderItem={renderPostItem}
          ListEmptyComponent={<Text style={styles.noPostsText}>Henüz gönderi yok.</Text>}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 30 }}
          onEndReachedThreshold={0.5}
          onEndReached={handleEndReached}
          removeClippedSubviews={true}
          maxToRenderPerBatch={3}
          updateCellsBatchingPeriod={100}
          initialNumToRender={2}
          windowSize={5}
          getItemLayout={(data, index) => ({
            length: 300, // Ortalama post yüksekliği
            offset: 300 * index,
            index,
          })}
          showsVerticalScrollIndicator={false}
        />
      )}
      {filterModalVisible && renderFilterModal()}
      {sortModalVisible && renderSortModal()}
      {likesModalVisible && renderLikesModal()}
      {optionsModalVisible && renderOptionsModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    paddingTop: 30,
    padding: 10
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  //////////////////////////////////////////////////////////////////////////
  // ÜST BAR
  //////////////////////////////////////////////////////////////////////////
  topHeader: {
    width: '100%',
    height: 30,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    marginBottom: 10,
    // backgroundColor dinamik olarak ayarlanıyor
  },
  headerLeft: {
    width: 60,
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingLeft: 5
  },
  sortButtonInHeader: {
    paddingHorizontal: 10
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  },
  filterButtonInHeader: {
    paddingHorizontal: 10
  },
  notificationButton: {
    paddingHorizontal: 8,
    marginLeft: 5,
    marginTop: -22,
    position: 'relative'
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: 0,
    backgroundColor: '#FF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#000'
  },
  notificationBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center'
  },
  topAvatar: {
    width: 45,
    height: 45,
    borderRadius: 23,
    borderWidth: 1,
    borderColor: '#0066CC',
    marginTop: -26
  },
  topTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: -20,
    marginRight: -2
  },
  //////////////////////////////////////////////////////////////////////////
  // TAB BAR
  //////////////////////////////////////////////////////////////////////////
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    backgroundColor: '#121212',
    marginBottom: 10,
    paddingHorizontal: 10,
    paddingVertical: 5
  },
  tabItem: {
    paddingVertical: 10,
    paddingHorizontal: 20
  },
  tabText: {
    fontSize: 16,
    color: '#aaa'
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#fff'
  },
  activeTabText: {
    color: '#fff',
    fontWeight: 'bold'
  },
  //////////////////////////////////////////////////////////////////////////
  // POST KARTI
  //////////////////////////////////////////////////////////////////////////
  card: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 15,
    marginBottom: 15
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#0066CC'
  },
  userInfoColumn: {
    marginLeft: 10,
    marginTop: -5
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3498db',
    marginBottom: 2
  },
  userRank: {
    fontSize: 12,
    color: '#FFD700',
    fontStyle: 'italic',
    marginTop: 1,
    textShadowColor: 'rgba(255, 215, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  followButton: {
    backgroundColor: '#0066CC',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
    marginTop: 2
  },
  followButtonText: {
    fontSize: 11,
    color: '#fff'
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  postTypeLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    marginRight: 8
  },
  optionsButton: {
    padding: 5
  },
  cardContent: {
    marginVertical: 10
  },
  content: {
    fontSize: 15,
    color: '#fff',
    lineHeight: 22
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#333',
    paddingTop: 10,
    justifyContent: 'space-between'
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  saveButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  actionText: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 5
  },
  timestampFooter: {
    fontSize: 12,
    color: '#aaa'
  },
  noPostsText: {
    color: '#aaa',
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16
  },
  /////////////////////////////////////////////////////////////////////////
  // Modal Overlay
  /////////////////////////////////////////////////////////////////////////
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.75)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  filterContainer: {
    backgroundColor: '#1a1a1a',
    width: '70%',
    borderRadius: 10,
    padding: 16
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 12,
    textAlign: 'center'
  },
  filterOption: {
    paddingVertical: 10
  },
  /////////////////////////////////////////////////////////////////////////
  // Likes Modal
  /////////////////////////////////////////////////////////////////////////
  modalContainer: {
    backgroundColor: '#1a1a1a',
    width: '80%',
    borderRadius: 12,
    padding: 20,
    maxHeight: '80%'
  },
  modalUserRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomColor: '#333',
    borderBottomWidth: 1
  },
  modalAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#0066CC'
  },
  modalUsername: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 10
  },
  modalCloseButton: {
    marginTop: 15,
    backgroundColor: '#0066CC',
    paddingVertical: 10,
    borderRadius: 6,
    alignItems: 'center'
  },
  modalCloseButtonText: {
    fontSize: 16,
    color: '#fff'
  },
  noLikesText: {
    color: '#aaa',
    textAlign: 'center',
    marginTop: 20,
    fontSize: 14
  },
  /////////////////////////////////////////////////////////////////////////
  // 3 Nokta Menüsü
  /////////////////////////////////////////////////////////////////////////
  optionsModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  optionsModalContainer: {
    width: '70%',
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 16
  },
  optionsModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center'
  },
  optionsModalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8
  },
  optionsModalOptionText: {
    color: '#fff',
    fontSize: 15,
    marginLeft: 10
  },
  /////////////////////////////////////////////////////////////////////////
  // Likes Modal Follow Button
  /////////////////////////////////////////////////////////////////////////
  followButtonModal: {
    backgroundColor: '#0066CC',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 10
  },
  followButtonModalText: {
    fontSize: 11,
    color: '#fff'
  },
  // Kalp animasyonu stilleri
  heartAnimation: {
    position: 'absolute',
    zIndex: 1000,
  }
});
