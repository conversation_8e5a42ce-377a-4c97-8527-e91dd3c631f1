import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Animated,
  Modal,
} from 'react-native';
import { auth, db } from '../firebase';
import {
  doc,
  onSnapshot,
  collection,
  query,
  where,
  updateDoc,
  arrayUnion,
  arrayRemove,
  increment,
  deleteDoc,
  getDocs,
  getDoc,
  serverTimestamp,
} from 'firebase/firestore';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import defaultAvatar from '../assets/default-avatar.png';
import { createNotification, NOTIFICATION_TYPES } from '../utils/notificationUtils';
import { updateUserPopularity, listenToUserPopularity, listenToPostComments } from '../utils/popularityUtils';

// Avatar mapping: ProfileEditScreen’de kaydedilen "avatarX" şeklinde.
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

// Yerel tanımlı MessageModal – uygulamanızdaki diğer şık modallarla uyumlu olacak şekilde
const MessageModal = ({ visible, message, onClose }) => {
  if (!visible) return null;
  return (
    <View style={styles.modalOverlay}>
      <View style={styles.modalContainer}>
        <Text style={styles.modalText}>{message}</Text>
        <TouchableOpacity style={styles.modalButton} onPress={onClose}>
          <Text style={styles.modalButtonText}>Tamam</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// ConfirmModal: Özellikle gönderi silme onayı için
const ConfirmModal = ({ visible, message, onCancel, onConfirm }) => {
  if (!visible) return null;
  return (
    <View style={styles.modalOverlay}>
      <View style={styles.modalContainer}>
        <Text style={styles.modalText}>{message}</Text>
        <View style={{ flexDirection: 'row', marginTop: 20 }}>
          <TouchableOpacity style={[styles.modalButton, { marginRight: 10 }]} onPress={onCancel}>
            <Text style={styles.modalButtonText}>İptal</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.modalButton} onPress={onConfirm}>
            <Text style={styles.modalButtonText}>Sil</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

// "x dakika önce" formatı
const timeAgo = (timestamp) => {
  if (!timestamp || !timestamp.toDate) return "Bilinmeyen Tarih";
  const postDate = timestamp.toDate();
  const now = new Date();
  const diffInSeconds = Math.floor((now - postDate) / 1000);
  if (diffInSeconds < 60) return diffInSeconds + " saniye önce";
  if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + " dakika önce";
  if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + " saat önce";
  return Math.floor(diffInSeconds / 86400) + " gün önce";
};

const Hesap = () => {
  const [userData, setUserData] = useState({});
  const [posts, setPosts] = useState([]);
  const [commentCounts, setCommentCounts] = useState({});
  const [loadingProfile, setLoadingProfile] = useState(true);
  const [loadingPosts, setLoadingPosts] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [popularity, setPopularity] = useState(0);
  const [userRank, setUserRank] = useState('');

  // Modal için state'ler
  const [messageModalVisible, setMessageModalVisible] = useState(false);
  const [messageModalText, setMessageModalText] = useState('');
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [postToDelete, setPostToDelete] = useState(null);

  // Beğeniler modal state'leri
  const [likesModalVisible, setLikesModalVisible] = useState(false);
  const [likesModalData, setLikesModalData] = useState([]);

  // Beğeni animasyonu için değişkenler
  const likeAnimValues = useRef({});
  const [processingLikes, setProcessingLikes] = useState({});

  const navigation = useNavigation();

  const showMessage = (msg) => {
    setMessageModalText(msg);
    setMessageModalVisible(true);
  };

  // Ekrana her odaklandığında güncel profil verilerini çekiyoruz.
  useFocusEffect(
    useCallback(() => {
      if (!auth.currentUser) {
        setLoadingProfile(false);
        return;
      }
      const userRef = doc(db, 'users', auth.currentUser.uid);
      const unsub = onSnapshot(userRef, (snap) => {
        if (snap.exists()) {
          const data = snap.data() || {};
          setUserData({
            ...data,
            followers: data.followers || [],
            following: data.following || [],
          });
        }
        setLoadingProfile(false);
      });
      return () => unsub();
    }, [])
  );

  // Gönderi aboneliği
  useFocusEffect(
    useCallback(() => {
      if (!auth.currentUser) {
        setLoadingPosts(false);
        return;
      }
      const postsRef = collection(db, 'posts');
      const q = query(postsRef, where('uid', '==', auth.currentUser.uid));
      const unsubPosts = onSnapshot(q, (snapshot) => {
        const arr = snapshot.docs.map((docSnap) => ({
          id: docSnap.id,
          ...docSnap.data(),
        }));
        const sorted = arr.sort((a, b) => b.createdAt.toDate() - a.createdAt.toDate());
        setPosts(sorted);
        setLoadingPosts(false);
        setRefreshing(false);
      });
      return () => unsubPosts && unsubPosts();
    }, [])
  );

  // Popülerlik hesaplama fonksiyonu
  const calculatePopularity = (userPosts, followers) => {
    let totalLikes = 0;
    let totalComments = 0;

    // Gönderilerdeki beğeni ve yorumları topla
    userPosts.forEach(post => {
      totalLikes += post.likes || 0;
      totalComments += commentCounts[post.id] || 0;
    });

    // Popülerlik puanı hesaplama: 10 beğeni = 1 puan, 3 yorum = 1 puan, 1 takipçi = 1 puan
    const likesPoints = Math.floor(totalLikes / 10);
    const commentsPoints = Math.floor(totalComments / 3);
    const followersPoints = followers || 0;

    const total = likesPoints + commentsPoints + followersPoints;

    // Popülerlik değerini Firestore'da güncelle
    if (auth.currentUser) {
      updateDoc(doc(db, 'users', auth.currentUser.uid), {
        popularity: total
      }).catch(err => console.error('Popülerlik güncelleme hatası:', err));
    }

    return total;
  };

  // Kullanıcı ünvanını belirle
  const determineRank = (popularityScore) => {
    if (popularityScore >= 1000) return 'Efsane';
    if (popularityScore >= 500) return 'Yıldız';
    if (popularityScore >= 200) return 'Popüler';
    if (popularityScore >= 100) return 'Yükselen';
    if (popularityScore >= 50) return 'Aktif';
    if (popularityScore >= 20) return 'Başlangıç';
    return 'Yeni Üye';
  };

  // Yorum sayılarını canlı olarak dinle
  useEffect(() => {
    if (posts.length === 0) return;

    const unsubscribes = {};

    // Her gönderi için yorum sayısını dinle
    posts.forEach(post => {
      unsubscribes[post.id] = listenToPostComments(post.id, (commentCount) => {
        setCommentCounts(prev => ({
          ...prev,
          [post.id]: commentCount
        }));
      });
    });

    // Component unmount olduğunda dinleyicileri temizle
    return () => {
      Object.values(unsubscribes).forEach(unsub => unsub && unsub());
    };
  }, [posts]);

  // Popülerlik ve ünvanı güncelle
  useEffect(() => {
    if (userData && posts.length > 0) {
      const popularityScore = calculatePopularity(posts, userData.followers?.length || 0);
      setPopularity(popularityScore);
      setUserRank(determineRank(popularityScore));
    }
  }, [posts, userData]);

  // Popülerlik değerini canlı olarak dinle
  useEffect(() => {
    if (!auth.currentUser) return;

    // Popülerlik değişimini dinle
    const unsubscribe = listenToUserPopularity(auth.currentUser.uid, (newPopularity) => {
      setPopularity(newPopularity);
      setUserRank(determineRank(newPopularity));
    });

    return () => unsubscribe();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    // useFocusEffect aboneliği sayesinde refresh için ekstra işlem gerekmez.
  };

  const requestDeletePost = (postId) => {
    setPostToDelete(postId);
    setConfirmVisible(true);
  };

  const handleDeletePost = async () => {
    setConfirmVisible(false);
    try {
      const commentsSnap = await getDocs(collection(db, "posts", postToDelete, "comments"));
      for (const c of commentsSnap.docs) {
        const repliesSnap = await getDocs(collection(db, "posts", postToDelete, "comments", c.id, "replies"));
        for (const r of repliesSnap.docs) {
          await deleteDoc(r.ref);
        }
        await deleteDoc(c.ref);
      }
      await deleteDoc(doc(db, "posts", postToDelete));
      showMessage("Gönderi başarıyla silindi.");
    } catch (e) {
      showMessage("Gönderi silinirken bir hata oluştu.");
      console.error("Post silme hatası:", e);
    }
  };



  // Beğeni işlemi



  // Çift tıklama için
  const lastTapRef = useRef({});
  const doubleTapDelayMs = 300;

  // Çift tıklama ile beğenme fonksiyonu
  const handleDoubleTap = (postId) => {
    // İşlem zaten devam ediyorsa çık
    if (processingLikes[postId]) return;

    // Kullanıcı giriş yapmamışsa çık
    if (!auth.currentUser) return;

    const now = Date.now();
    const lastTap = lastTapRef.current[postId] || 0;

    // Son tıklama zamanını güncelle
    lastTapRef.current[postId] = now;

    // Eğer iki tıklama arasındaki süre belirlenen süreden kısaysa çift tıklama olarak kabul et
    if (now - lastTap < doubleTapDelayMs) {
      // Post'u bul
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      // Beğeni durumunu kontrol et ve beğeni işlemini başlat
      // Çift tıklamada her zaman beğeni işlemini başlat (kalbe basma ile aynı davranış)
      toggleLike(postId);
    }
  };

  // Kullanılmayan showHeartAnimation fonksiyonu kaldırıldı

  const toggleLike = (postId) => {
    if (!auth.currentUser) return;

    // Beğeni işlemi devam ediyorsa çık
    if (processingLikes[postId]) return;

    // İşlem başladı - Hemen işlemi kilitle
    setProcessingLikes(prev => ({ ...prev, [postId]: true }));

    // Beğeni animasyonu için Animated.Value oluştur
    if (!likeAnimValues.current[postId]) {
      likeAnimValues.current[postId] = new Animated.Value(1);
    }

    // Önce mevcut durumu kontrol et
    const post = posts.find(p => p.id === postId);
    if (!post) {
      setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      return;
    }

    // Arka planda Firestore işlemlerini yap
    const postRef = doc(db, 'posts', postId);

    getDoc(postRef).then(snap => {
      if (!snap.exists()) {
        // Post bulunamadı
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        return;
      }

      const data = snap.data();
      const serverIsLiked = data.likedBy?.includes(auth.currentUser.uid);

      // Sunucu durumu ile istemci durumu farklıysa, sunucu durumunu kullan
      // Bu, hızlı ardışık beğeni işlemlerinde tutarlılık sağlar
      const finalIsLiked = serverIsLiked;

      // Beğeni sayısı 0'dan küçük olamaz
      const newLikes = finalIsLiked ? Math.max(0, data.likes - 1) : (data.likes || 0) + 1;

      // Animasyonu başlat (UI güncellemesinden önce)
      if (finalIsLiked) {
        // Beğeni kaldırma animasyonu
        Animated.sequence([
          Animated.spring(likeAnimValues.current[postId], {
            toValue: 0.8,
            friction: 3,
            tension: 100,
            useNativeDriver: true
          }),
          Animated.spring(likeAnimValues.current[postId], {
            toValue: 1,
            friction: 3,
            tension: 80,
            useNativeDriver: true
          })
        ]).start();
      } else {
        // Beğeni ekleme animasyonu
        Animated.sequence([
          Animated.spring(likeAnimValues.current[postId], {
            toValue: 1.3,
            friction: 2,
            tension: 120,
            useNativeDriver: true
          }),
          Animated.spring(likeAnimValues.current[postId], {
            toValue: 1,
            friction: 3,
            tension: 80,
            useNativeDriver: true
          })
        ]).start();
      }

      // UI'ı optimize edilmiş şekilde güncelle
      setPosts(prev => {
        const postIndex = prev.findIndex(p => p.id === postId);
        if (postIndex === -1) return prev;

        const updatedPost = { ...prev[postIndex] };
        updatedPost.likes = newLikes;

        if (finalIsLiked) {
          updatedPost.likedBy = updatedPost.likedBy?.filter(uid => uid !== auth.currentUser.uid) || [];
        } else {
          updatedPost.likedBy = [...(updatedPost.likedBy || []), auth.currentUser.uid];
        }

        const newPosts = [...prev];
        newPosts[postIndex] = updatedPost;
        return newPosts;
      });

      // Firestore güncelleme
      updateDoc(postRef, {
        likes: newLikes, // increment yerine doğrudan değer atama
        likedBy: finalIsLiked
          ? arrayRemove(auth.currentUser.uid)
          : arrayUnion(auth.currentUser.uid),
        lastLikeUpdate: serverTimestamp()
      }).then(() => {
        // Beğeni bildirimi oluştur
        if (!finalIsLiked && data.uid !== auth.currentUser.uid) {
          createNotification(
            NOTIFICATION_TYPES.LIKE,
            auth.currentUser.uid,
            data.uid,
            postId
          );
        }

        // Popülerlik değerini güncelle
        if (data.uid) {
          // Zorla güncelleme yap (true) ve sonucu bekle
          return updateUserPopularity(data.uid, true);
        }
      }).catch(() => {
        // Beğeni güncelleme hatası
      }).finally(() => {
        // İşlem bitti - 500ms sonra kilidi kaldır (hızlı ardışık tıklamaları engelle)
        setTimeout(() => {
          setProcessingLikes(prev => ({ ...prev, [postId]: false }));
        }, 500);
      });
    }).catch(() => {
      // toggleLike hata
      // İşlem bitti - 500ms sonra kilidi kaldır
      setTimeout(() => {
        setProcessingLikes(prev => ({ ...prev, [postId]: false }));
      }, 500);
    });
  };

  // Beğenenleri göster
  const handleShowLikes = (likedBy) => {
    if (!likedBy || likedBy.length === 0) return;

    setLikesModalData(likedBy);
    setLikesModalVisible(true);
  };

  const handleCommentPress = (postId) => {
    navigation.navigate('Comments', { postId });
  };

  const renderPostItem = ({ item }) => {
    const isCurrentUser = item.uid === auth.currentUser.uid;
    const displayName = isCurrentUser ? userData.username : item.username;
    const displayProfilePic =
      isCurrentUser && userData.profilePic
        ? (avatarMap[userData.profilePic] || defaultAvatar)
        : item.profilePic
          ? (avatarMap[item.profilePic] || defaultAvatar)
          : defaultAvatar;

    const isLiked = item.likedBy?.includes(auth.currentUser.uid);
    const commentCount = commentCounts[item.id] || 0;

    // Kullanıcı ünvanını belirle
    const userRank =
      item.popularity >= 1000 ? 'Efsane' :
      item.popularity >= 500 ? 'Yıldız' :
      item.popularity >= 200 ? 'Popüler' :
      item.popularity >= 100 ? 'Yükselen' :
      item.popularity >= 50 ? 'Aktif' :
      item.popularity >= 20 ? 'Başlangıç' :
      item.popularity > 0 ? 'Yeni Üye' : '';
    return (
      <View style={styles.card} key={item.id}>
        <View style={styles.postHeader}>
          <View style={styles.headerLeft}>
            <TouchableOpacity onPress={() => navigation.navigate('OtherProfile', { uid: item.uid })}>
              <Image source={displayProfilePic} style={styles.avatar} />
            </TouchableOpacity>
            <View style={styles.headerInfo}>
              <TouchableOpacity onPress={() => navigation.navigate('OtherProfile', { uid: item.uid })}>
                <Text style={styles.username}>{displayName || 'Bilinmeyen Kullanıcı'}</Text>
                <Text style={styles.rankBadge}>{userRank || 'Yeni Üye'}</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.headerRight}>
            <Text style={styles.postTypeLabel}>
              {item.type === 'Şarkı Sözü'
                ? 'Şarkı'
                : item.type === 'Şiir'
                ? 'Şiir'
                : item.type || 'Diğer'}
            </Text>
            {item.uid === auth.currentUser.uid && (
              <TouchableOpacity style={styles.optionsButton} onPress={() => requestDeletePost(item.id)}>
                <Ionicons name="trash" size={20} color="#fff" />
              </TouchableOpacity>
            )}
          </View>
        </View>
        <TouchableOpacity
          style={styles.cardContent}
          activeOpacity={0.9}
          onPress={() => handleDoubleTap(item.id)}
        >
          <Text style={styles.content}>{item.content}</Text>


        </TouchableOpacity>
        <View style={styles.cardFooter}>
          <View style={styles.footerLeft}>
            <TouchableOpacity
              style={styles.likeContainer}
              onPress={() => toggleLike(item.id)}
              onLongPress={() => handleShowLikes(item.likedBy)}
              disabled={processingLikes[item.id]}
              hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              activeOpacity={0.6}
            >
              <Animated.View style={{ transform: [{ scale: likeAnimValues.current[item.id] || new Animated.Value(1) }] }}>
                <Ionicons
                  name={isLiked ? "heart" : "heart-outline"}
                  size={24}
                  color={isLiked ? "#e74c3c" : "#fff"}
                />
              </Animated.View>
              <Text style={[styles.actionText, { marginLeft: 6 }]}>{item.likes || 0}</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={() => handleCommentPress(item.id)}>
              <Ionicons name="chatbubble-outline" size={24} color="#fff" />
              <Text style={styles.actionText}>Yorum ({commentCount})</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.timestampFooter}>{timeAgo(item.createdAt)}</Text>
        </View>
      </View>
    );
  };

  const renderProfileHeader = () => {
    if (loadingProfile) {
      return (
        <View style={{ padding: 20 }}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      );
    }
    return (
      <View style={styles.profileSection}>
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <Image
              source={userData?.profilePic ? (avatarMap[userData.profilePic] || defaultAvatar) : defaultAvatar}
              style={styles.profileImage}
            />
            <View style={styles.popularityContainer}>
              <Text style={styles.userRank}>{popularity} popülerlik</Text>
            </View>
            {userRank && (
              <View style={styles.rankContainer}>
                <Text style={styles.rankText}>{userRank}</Text>
              </View>
            )}
          </View>
          <View style={styles.infoContainer}>
            <View style={styles.nameContainer}>
              <Text style={styles.name}>{userData?.username || 'Bilinmeyen Kullanıcı'}</Text>
            </View>
            <Text
              style={[
                styles.bio,
                userData?.bio && userData.bio.length > 100 ? { fontSize: 10 } :
                userData?.bio && userData.bio.length > 80 ? { fontSize: 11 } :
                userData?.bio && userData.bio.length > 60 ? { fontSize: 12 } :
                userData?.bio && userData.bio.length > 40 ? { fontSize: 13 } :
                { fontSize: 14 }
              ]}
              numberOfLines={5}
            >
              {userData?.bio || 'Henüz biyografi eklenmemiş.'}
            </Text>
            <View style={styles.statsContainer}>
              <View style={styles.stat}>
                <Text style={styles.statNumber}>{posts.length}</Text>
                <Text style={styles.statLabel}>Gönderi</Text>
              </View>
              <TouchableOpacity
                style={styles.stat}
                onPress={() =>
                  navigation.navigate('FollowListScreen', {
                    type: 'followers',
                    userIds: userData.followers || [],
                    isCurrentUserProfile: true
                  })
                }
              >
                <Text style={styles.statNumber}>{userData.followers.length}</Text>
                <Text style={styles.statLabel}>Takipçi</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.stat}
                onPress={() =>
                  navigation.navigate('FollowListScreen', {
                    type: 'following',
                    userIds: userData.following || [],
                    isCurrentUserProfile: true
                  })
                }
              >
                <Text style={styles.statNumber}>{userData.following.length}</Text>
                <Text style={styles.statLabel}>Takip</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={styles.editButton}
          onPress={() => navigation.navigate('ProfiliDüzenle')}
        >
          <Text style={styles.editButtonText}>Profili Düzenle</Text>
        </TouchableOpacity>

        <Text style={styles.sectionTitle}>Gönderiler</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {loadingPosts && posts.length === 0 ? (
        <ActivityIndicator size="large" color="#fff" style={{ marginTop: 50 }} />
      ) : (
        <FlatList
          data={posts.sort((a, b) => b.createdAt.toDate() - a.createdAt.toDate())}
          keyExtractor={(item) => item.id}
          renderItem={renderPostItem}
          ListEmptyComponent={!loadingPosts && (<Text style={styles.noPostsText}>Henüz gönderi eklenmemiş.</Text>)}
          ListHeaderComponent={renderProfileHeader}
          contentContainerStyle={styles.postsContainer}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />}
        />
      )}
      <MessageModal visible={messageModalVisible} message={messageModalText} onClose={() => setMessageModalVisible(false)} />
      <ConfirmModal
        visible={confirmVisible}
        message="Gönderiyi silmek istediğinize emin misiniz?"
        onCancel={() => setConfirmVisible(false)}
        onConfirm={handleDeletePost}
      />

      {/* Beğeniler Modal */}
      {likesModalVisible && (
        <Modal
          animationType="slide"
          transparent={true}
          visible={likesModalVisible}
          onRequestClose={() => setLikesModalVisible(false)}
        >
          <View style={styles.likesModalOverlay}>
            <View style={styles.likesModalContent}>
              <View style={styles.likesModalHeader}>
                <Text style={styles.likesModalTitle}>Beğenenler</Text>
                <TouchableOpacity
                  onPress={() => setLikesModalVisible(false)}
                  style={styles.likesModalCloseButton}
                >
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              <FlatList
                data={likesModalData}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item: userId }) => (
                  <LikeUserItem userId={userId} />
                )}
                style={styles.likesModalList}
              />
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
};

// Beğeni modal'ındaki kullanıcı item'ı
const LikeUserItem = ({ userId }) => {
  const [userInfo, setUserInfo] = useState(null);
  const navigation = useNavigation();
  const currentUser = auth.currentUser;

  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          setUserInfo(userSnap.data());
        }
      } catch (error) {
        console.error('Kullanıcı bilgisi alınırken hata:', error);
      }
    };

    fetchUserInfo();
  }, [userId]);

  if (!userInfo) return null;

  return (
    <TouchableOpacity
      style={styles.likeUserItem}
      onPress={() => {
        if (userId === currentUser?.uid) {
          navigation.navigate('MainTabs', { screen: 'Profil' });
        } else {
          navigation.navigate('OtherProfile', { uid: userId });
        }
      }}
    >
      <Image
        source={
          userInfo.profilePic && avatarMap[userInfo.profilePic]
            ? avatarMap[userInfo.profilePic]
            : defaultAvatar
        }
        style={styles.likeUserAvatar}
      />
      <Text style={styles.likeUserName}>
        {userInfo.username || 'Kullanıcı'}
      </Text>
    </TouchableOpacity>
  );
};

export default Hesap;

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: '#000',
  },
  profileSection: {
    backgroundColor: '#000',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    height: 180, // Sabit yükseklik
  },
  avatarContainer: {
    alignItems: 'center',
    marginRight: 10,
    width: 110, // Sabit genişlik
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#0066CC',
  },
  popularityContainer: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderRadius: 12,
    paddingVertical: 3,
    paddingHorizontal: 8,
    marginTop: 13,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#FFD700',
    width: '100%',
  },
  rankContainer: {
    backgroundColor: '#FFD700',
    borderRadius: 12,
    paddingVertical: 2,
    paddingHorizontal: 8,
    marginTop: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '80%',
    height: 22,
  },
  rankText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
    lineHeight: 18,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  infoContainer: {
    flex: 1,
    marginLeft: 5,
    minHeight: 180, // Minimum yükseklik
    position: 'relative', // Göreceli konumlandırma
    paddingBottom: 60, // statsContainer için alan
    justifyContent: 'flex-start', // İçeriği üstten başlat
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    flexWrap: 'wrap',
  },
  name: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginRight: 8,
    flexShrink: 1,
  },
  rankBadge: {
    fontSize: 12,
    color: '#FFD700',
    fontStyle: 'italic',
    marginTop: 1,
    textShadowColor: 'rgba(255, 215, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  bio: {
    fontSize: 14, // Başlangıç yazı boyutu
    color: '#aaa',
    marginVertical: 5,
    flexShrink: 1, // İçeriğe göre küçülmesine izin ver
    flexWrap: 'wrap', // Metni sarma
    lineHeight: 16, // Satır yüksekliğini azalt
  },

  statsContainer: {
    flexDirection: 'row',
    marginVertical: 10,
    justifyContent: 'space-around',
    height: 50, // Sabit yükseklik
    position: 'absolute', // Mutlak konumlandırma
    bottom: 0, // Alt kısma sabitle
    left: 0,
    right: 0,
  },
  stat: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    height: 50, // Sabit yükseklik
  },
  statNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    height: 25, // Sabit yükseklik
    textAlignVertical: 'center',
  },
  statLabel: {
    fontSize: 14,
    color: '#aaa',
    lineHeight: 18,
    height: 20, // Sabit yükseklik
    textAlignVertical: 'center',
  },
  editButton: {
    backgroundColor: '#0066CC',
    paddingVertical: 12,
    borderRadius: 20,
    marginTop: 10,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  editButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  sectionTitle: {
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
    lineHeight: 22,
  },
  postsContainer: {
    paddingBottom: 30,
  },
  noPostsText: {
    color: '#fff',
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
  },
  card: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 15,
    marginHorizontal: 10,
    marginBottom: 15,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#0066CC',
  },
  headerInfo: {
    marginLeft: 10,
    marginTop: -5,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3498db',
    marginBottom: 2,
  },
  userRank: {
    fontSize: 12,
    color: '#FFD700',
    fontStyle: 'italic',
    marginTop: 1,
    textShadowColor: 'rgba(255, 215, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  postTypeLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
    marginRight: 8,
  },
  optionsButton: {
    padding: 5,
  },
  cardContent: {
    marginVertical: 10,
  },
  content: {
    fontSize: 15,
    color: '#fff',
    lineHeight: 22,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#333',
    paddingTop: 10,
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  actionText: {
    color: '#fff',
    marginLeft: 5,
    fontSize: 16,
  },
  timestampFooter: {
    fontSize: 12,
    color: '#aaa',
    alignSelf: 'flex-end',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContainer: {
    backgroundColor: '#222',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    alignItems: 'center',
  },
  modalText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
  modalButton: {
    backgroundColor: '#1DA1F2',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 6,
    marginTop: 20,
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  // Beğeniler modal stilleri
  likesModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  likesModalContent: {
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxHeight: '60%',
  },
  likesModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  likesModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  likesModalCloseButton: {
    padding: 5,
  },
  likesModalList: {
    maxHeight: 300,
  },
  likeUserItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 5,
  },
  likeUserAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  likeUserName: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
});
