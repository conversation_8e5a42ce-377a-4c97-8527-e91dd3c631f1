import React, { createContext, useContext, useState, useCallback } from 'react';

const FollowStatusContext = createContext();

export const useFollowStatus = () => {
  const context = useContext(FollowStatusContext);
  if (!context) {
    throw new Error('useFollowStatus must be used within a FollowStatusProvider');
  }
  return context;
};

export const FollowStatusProvider = ({ children }) => {
  // Global takip durumları
  const [followingUsers, setFollowingUsers] = useState(new Set());
  const [pendingRequests, setPendingRequests] = useState(new Set());

  // Takip durumunu güncelle
  const updateFollowStatus = useCallback((userId, isFollowing) => {
    setFollowingUsers(prev => {
      const newSet = new Set(prev);
      if (isFollowing) {
        newSet.add(userId);
        // Takip ediyorsa pending request'i kaldır
        setPendingRequests(prevPending => {
          const newPendingSet = new Set(prevPending);
          newPendingSet.delete(userId);
          return newPendingSet;
        });
      } else {
        newSet.delete(userId);
      }
      return newSet;
    });
  }, []);

  // Pending request durumunu güncelle
  const updatePendingRequest = useCallback((userId, isPending) => {
    setPendingRequests(prev => {
      const newSet = new Set(prev);
      if (isPending) {
        newSet.add(userId);
      } else {
        newSet.delete(userId);
      }
      return newSet;
    });
  }, []);

  // Kullanıcının takip durumunu kontrol et
  const isFollowing = useCallback((userId) => {
    return followingUsers.has(userId);
  }, [followingUsers]);

  // Kullanıcının pending request durumunu kontrol et
  const hasPendingRequest = useCallback((userId) => {
    return pendingRequests.has(userId);
  }, [pendingRequests]);

  // Toplu güncelleme - kullanıcının takip ettiği tüm kişileri set et
  const setFollowingList = useCallback((userIds) => {
    setFollowingUsers(new Set(userIds));
  }, []);

  // Toplu güncelleme - kullanıcının pending request'lerini set et
  const setPendingRequestsList = useCallback((userIds) => {
    setPendingRequests(new Set(userIds));
  }, []);

  // Takip işlemi başarılı olduğunda çağrılacak
  const handleFollowSuccess = useCallback((userId) => {
    updateFollowStatus(userId, true);
  }, [updateFollowStatus]);

  // Takip bırakma işlemi başarılı olduğunda çağrılacak
  const handleUnfollowSuccess = useCallback((userId) => {
    updateFollowStatus(userId, false);
  }, [updateFollowStatus]);

  // Takip isteği gönderme başarılı olduğunda çağrılacak
  const handleFollowRequestSent = useCallback((userId) => {
    updatePendingRequest(userId, true);
  }, [updatePendingRequest]);

  // Takip isteği iptal etme başarılı olduğunda çağrılacak
  const handleFollowRequestCancelled = useCallback((userId) => {
    updatePendingRequest(userId, false);
  }, [updatePendingRequest]);

  // Takip isteği kabul edildiğinde çağrılacak
  const handleFollowRequestAccepted = useCallback((userId) => {
    updatePendingRequest(userId, false);
    updateFollowStatus(userId, true);
  }, [updatePendingRequest, updateFollowStatus]);

  const value = {
    // State
    followingUsers,
    pendingRequests,
    
    // Kontrol fonksiyonları
    isFollowing,
    hasPendingRequest,
    
    // Güncelleme fonksiyonları
    updateFollowStatus,
    updatePendingRequest,
    setFollowingList,
    setPendingRequestsList,
    
    // İşlem başarı callback'leri
    handleFollowSuccess,
    handleUnfollowSuccess,
    handleFollowRequestSent,
    handleFollowRequestCancelled,
    handleFollowRequestAccepted,
  };

  return (
    <FollowStatusContext.Provider value={value}>
      {children}
    </FollowStatusContext.Provider>
  );
};
