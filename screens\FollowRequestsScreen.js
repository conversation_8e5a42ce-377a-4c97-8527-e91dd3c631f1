import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { auth } from '../firebase';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import {
  getIncomingFollowRequests,
  acceptFollowRequest,
  rejectFollowRequest
} from '../utils/followRequestUtils';

// Avatar haritası
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
};

export default function FollowRequestsScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { translations } = useLanguage();
  
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [processingRequests, setProcessingRequests] = useState({});

  useEffect(() => {
    loadFollowRequests();
  }, []);

  const loadFollowRequests = async () => {
    if (!auth.currentUser) return;
    
    try {
      const incomingRequests = await getIncomingFollowRequests(auth.currentUser.uid);
      setRequests(incomingRequests);
    } catch (error) {
      console.error('Takip istekleri yükleme hatası:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFollowRequests();
    setRefreshing(false);
  };

  const handleBatchAccept = () => {
    Alert.alert(
      'Toplu Kabul',
      'Tüm takip isteklerini kabul etmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Kabul Et',
          onPress: async () => {
            for (const request of requests) {
              if (!processingRequests[request.id]) {
                await handleAcceptRequest(request.id, request.fromUserId);
              }
            }
          }
        }
      ]
    );
  };

  const handleBatchReject = () => {
    Alert.alert(
      'Toplu Reddet',
      'Tüm takip isteklerini reddetmek istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Reddet',
          style: 'destructive',
          onPress: async () => {
            for (const request of requests) {
              if (!processingRequests[request.id]) {
                await handleRejectRequest(request.id, request.fromUserId);
              }
            }
          }
        }
      ]
    );
  };

  const handleAcceptRequest = async (requestId, fromUserId) => {
    if (processingRequests[requestId]) return;

    setProcessingRequests(prev => ({ ...prev, [requestId]: true }));

    try {
      const success = await acceptFollowRequest(fromUserId, auth.currentUser.uid);
      
      if (success) {
        // İsteği listeden kaldır
        setRequests(prev => prev.filter(req => req.id !== requestId));
        Alert.alert('Başarılı', 'Takip isteği kabul edildi.');
      } else {
        Alert.alert('Hata', 'Takip isteği kabul edilirken bir hata oluştu.');
      }
    } catch (error) {
      console.error('Takip isteği kabul etme hatası:', error);
      Alert.alert('Hata', 'Takip isteği kabul edilirken bir hata oluştu.');
    } finally {
      setProcessingRequests(prev => ({ ...prev, [requestId]: false }));
    }
  };

  const handleRejectRequest = async (requestId, fromUserId) => {
    if (processingRequests[requestId]) return;

    setProcessingRequests(prev => ({ ...prev, [requestId]: true }));

    try {
      const success = await rejectFollowRequest(fromUserId, auth.currentUser.uid);
      
      if (success) {
        // İsteği listeden kaldır
        setRequests(prev => prev.filter(req => req.id !== requestId));
        Alert.alert('Başarılı', 'Takip isteği reddedildi.');
      } else {
        Alert.alert('Hata', 'Takip isteği reddedilirken bir hata oluştu.');
      }
    } catch (error) {
      console.error('Takip isteği reddetme hatası:', error);
      Alert.alert('Hata', 'Takip isteği reddedilirken bir hata oluştu.');
    } finally {
      setProcessingRequests(prev => ({ ...prev, [requestId]: false }));
    }
  };

  const navigateToProfile = (userId) => {
    navigation.navigate('OtherProfile', { uid: userId });
  };

  const renderRequestItem = ({ item }) => {
    const isProcessing = processingRequests[item.id];
    const fromUser = item.fromUser || {};
    
    return (
      <View style={[styles.requestItem, { backgroundColor: theme.cardBackground }]}>
        <TouchableOpacity 
          style={styles.userInfo}
          onPress={() => navigateToProfile(item.fromUserId)}
        >
          <Image
            style={styles.avatar}
            source={
              fromUser.profilePic
                ? (avatarMap[fromUser.profilePic] || require('../assets/default-avatar.png'))
                : require('../assets/default-avatar.png')
            }
          />
          <View style={styles.userDetails}>
            <Text style={[styles.username, { color: theme.text }]}>
              {fromUser.username || 'Kullanıcı'}
            </Text>
            <Text style={[styles.fullName, { color: theme.secondaryText }]}>
              {fromUser.fullName || fromUser.email || ''}
            </Text>
          </View>
        </TouchableOpacity>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.acceptButton, isProcessing && styles.disabledButton]}
            onPress={() => handleAcceptRequest(item.id, item.fromUserId)}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.acceptButtonText}>Kabul Et</Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.rejectButton, isProcessing && styles.disabledButton]}
            onPress={() => handleRejectRequest(item.id, item.fromUserId)}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <ActivityIndicator size="small" color="#666" />
            ) : (
              <Text style={styles.rejectButtonText}>Reddet</Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.background }]}>
        <ActivityIndicator size="large" color="#1DA1F2" />
        <Text style={[styles.loadingText, { color: theme.text }]}>
          Takip istekleri yükleniyor...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <View style={[styles.header, { backgroundColor: theme.cardBackground }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>
          Takip İstekleri
        </Text>
        <View style={styles.placeholder} />
      </View>

      {requests.length > 0 && (
        <View style={[styles.batchActions, { backgroundColor: theme.cardBackground }]}>
          <TouchableOpacity
            style={styles.batchAcceptButton}
            onPress={handleBatchAccept}
          >
            <Ionicons name="checkmark-done" size={16} color="#fff" />
            <Text style={styles.batchButtonText}>Tümünü Kabul Et</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.batchRejectButton}
            onPress={handleBatchReject}
          >
            <Ionicons name="close-circle" size={16} color="#fff" />
            <Text style={styles.batchButtonText}>Tümünü Reddet</Text>
          </TouchableOpacity>
        </View>
      )}

      {requests.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="people-outline" size={64} color={theme.secondaryText} />
          <Text style={[styles.emptyText, { color: theme.secondaryText }]}>
            Henüz takip isteğiniz yok
          </Text>
        </View>
      ) : (
        <FlatList
          data={requests}
          renderItem={renderRequestItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#1DA1F2']}
              tintColor="#1DA1F2"
            />
          }
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e8ed',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  listContainer: {
    padding: 16,
  },
  requestItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  fullName: {
    fontSize: 14,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  acceptButton: {
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  rejectButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.6,
  },
  acceptButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  rejectButtonText: {
    color: '#666',
    fontWeight: 'bold',
    fontSize: 14,
  },
  batchActions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e1e8ed',
  },
  batchAcceptButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#27ae60',
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  batchRejectButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#e74c3c',
    paddingVertical: 10,
    borderRadius: 8,
    gap: 6,
  },
  batchButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});
